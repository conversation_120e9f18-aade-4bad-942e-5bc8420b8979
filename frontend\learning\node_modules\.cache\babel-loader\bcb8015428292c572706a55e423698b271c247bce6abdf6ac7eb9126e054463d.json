{"ast": null, "code": "var _jsxFileName = \"D:\\\\projects\\\\AIstrusys\\\\frontend\\\\learning\\\\src\\\\components\\\\LearningModule\\\\scenes\\\\ResultScreen.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useRef, useCallback } from 'react';\nimport { Typo<PERSON>, Button, Modal, Tag, message } from 'antd';\nimport { CheckCircleOutlined, RightOutlined, CloseCircleOutlined, ExclamationCircleOutlined, PlayCircleOutlined, CloseOutlined } from '@ant-design/icons';\nimport ReactPlayer from 'react-player';\nimport PropTypes from 'prop-types';\nimport '../../../styles/SceneStyles.css';\nimport './ResultScreen.css';\nimport HtmlContentRenderer from '../../content/HtmlContentRenderer';\nimport { ContentBlockList } from '../../content/ContentBlockRenderer';\nimport { LearningAPI } from '../../../api/learning';\nimport { MathJax } from 'better-react-mathjax';\nimport QuestionDetailModal from '../../common/QuestionDetailModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\n\n/**\r\n * 结果场景组件\r\n *\r\n * @param {Object} props 组件属性\r\n * @param {Object} props.knowledgePoint 知识点数据\r\n * @param {number} props.progress 学习进度\r\n * @param {Object} props.userAnswers 用户答案\r\n * @param {Array} props.questions 题目列表\r\n * @param {Function} props.onRestart 重新开始学习的回调函数\r\n * @param {Function} props.onViewQuestions 查看题目的回调函数\r\n * @param {number} props.sessionId 可选的练习会话ID，用于从数据库获取数据\r\n * @param {number} props.testStartTime 测试开始时间戳\r\n * @param {number} props.testEndTime 测试结束时间戳\r\n * @returns {JSX.Element} 结果场景组件\r\n */\nconst ResultScreen = ({\n  knowledgePoint,\n  progress,\n  userAnswers = {},\n  questions = [],\n  onRestart,\n  onViewQuestions,\n  sessionId,\n  isHistoryView = false,\n  testStartTime,\n  testEndTime\n}) => {\n  _s();\n  console.log('ResultScreen - 组件初始化, props:', {\n    knowledgePoint: knowledgePoint === null || knowledgePoint === void 0 ? void 0 : knowledgePoint.name,\n    questionsCount: questions.length,\n    userAnswersCount: Object.keys(userAnswers).length,\n    sessionId: sessionId\n  });\n\n  // 状态管理\n  const [selectedQuestion, setSelectedQuestion] = useState(null);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [practiceResultSaved, setPracticeResultSaved] = useState(false);\n  const [dbData, setDbData] = useState(null);\n  const [loadingDbData, setLoadingDbData] = useState(false);\n  const [currentSessionId, setCurrentSessionId] = useState(sessionId);\n\n  // 知识点解析弹窗状态\n  const [knowledgeAnalysisVisible, setKnowledgeAnalysisVisible] = useState(false);\n  const [currentVideo, setCurrentVideo] = useState(null);\n  const [playing, setPlaying] = useState(false);\n  const [playbackRate, setPlaybackRate] = useState(1);\n  const [videoList, setVideoList] = useState([]);\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\n  const [loadingVideos, setLoadingVideos] = useState(false);\n  console.log('ResultScreen - 初始状态:', {\n    currentSessionId,\n    hasDbData: !!dbData,\n    loadingDbData\n  });\n\n  // 使用ref防止重复保存\n  const saveInProgress = useRef(false);\n\n  // 从数据库获取练习数据\n  useEffect(() => {\n    console.log('ResultScreen - useEffect触发, currentSessionId:', currentSessionId, 'dbData:', !!dbData, 'loadingDbData:', loadingDbData);\n    const fetchPracticeData = async () => {\n      // 如果提供了sessionId，从数据库获取数据\n      if (currentSessionId && !dbData && !loadingDbData) {\n        console.log('ResultScreen - 开始从数据库获取练习数据, sessionId:', currentSessionId);\n        setLoadingDbData(true);\n        try {\n          console.log('ResultScreen - 调用getPracticeDetail API');\n          const practiceDetail = await LearningAPI.getPracticeDetail(currentSessionId);\n          console.log('ResultScreen - getPracticeDetail API响应:', practiceDetail);\n          const convertedData = await LearningAPI.convertPracticeDetailToProps(practiceDetail);\n          console.log('ResultScreen - 数据转换结果:', convertedData);\n          if (convertedData) {\n            console.log('ResultScreen - 数据库数据获取成功，设置dbData');\n            setDbData(convertedData);\n          } else {\n            console.warn('ResultScreen - 数据库数据转换失败');\n          }\n        } catch (error) {\n          console.error('ResultScreen - 获取数据库数据失败:', error);\n        } finally {\n          console.log('ResultScreen - 数据获取完成，设置loadingDbData为false');\n          setLoadingDbData(false);\n        }\n      } else {\n        console.log('ResultScreen - 跳过数据获取，原因:', {\n          hasSessionId: !!currentSessionId,\n          hasDbData: !!dbData,\n          isLoading: loadingDbData\n        });\n      }\n    };\n    fetchPracticeData();\n  }, [currentSessionId, dbData, loadingDbData]);\n\n  // 确定数据来源：优先使用数据库数据，然后是props数据\n  const effectiveKnowledgePoint = (dbData === null || dbData === void 0 ? void 0 : dbData.knowledgePoint) || knowledgePoint;\n  const effectiveQuestions = (dbData === null || dbData === void 0 ? void 0 : dbData.questions) || questions;\n  const effectiveUserAnswers = (dbData === null || dbData === void 0 ? void 0 : dbData.userAnswers) || userAnswers;\n\n  // 确保questions是数组，userAnswers是对象\n  const validQuestions = Array.isArray(effectiveQuestions) ? effectiveQuestions : [];\n  const validUserAnswers = effectiveUserAnswers && typeof effectiveUserAnswers === 'object' ? effectiveUserAnswers : {};\n\n  // 保存练习结果\n  useEffect(() => {\n    const savePracticeResult = async () => {\n      // 避免重复保存\n      if (practiceResultSaved || saveInProgress.current || !(knowledgePoint !== null && knowledgePoint !== void 0 && knowledgePoint.id) || validQuestions.length === 0) {\n        console.log('ResultScreen - 跳过保存:', {\n          practiceResultSaved,\n          saveInProgress: saveInProgress.current,\n          hasKnowledgePoint: !!(knowledgePoint !== null && knowledgePoint !== void 0 && knowledgePoint.id),\n          questionsCount: validQuestions.length\n        });\n        return;\n      }\n      console.log('ResultScreen - 开始保存练习结果');\n      saveInProgress.current = true;\n      try {\n        // 计算实际测试时长\n        const actualDurationSeconds = testStartTime && testEndTime ? Math.floor((testEndTime - testStartTime) / 1000) : validQuestions.length * 60; // 如果没有时间记录，按每题1分钟估算\n\n        // 构建练习数据\n        const practiceData = {\n          knowledgePointId: knowledgePoint.id,\n          sessionType: 'KNOWLEDGE_POINT_PRACTICE',\n          durationSeconds: actualDurationSeconds,\n          // 添加实际测试时长\n          questions: validQuestions.map((question, index) => {\n            var _question$questionDet;\n            const questionAnswer = validUserAnswers[question.id];\n\n            // 确定答题状态\n            let answerStatus = 'NOT_ANSWERED';\n            if (questionAnswer) {\n              if (questionAnswer.isSkipped) {\n                answerStatus = 'SKIPPED';\n              } else if (questionAnswer.isCorrect) {\n                answerStatus = 'CORRECT';\n              } else {\n                answerStatus = 'INCORRECT';\n              }\n            }\n\n            // 获取学生的真实输入答案\n            let studentAnswer = null;\n            if (questionAnswer && !questionAnswer.isSkipped) {\n              // 提取学生的原始输入数据（排除系统添加的字段）\n              const {\n                isCorrect,\n                isSkipped,\n                feedback,\n                ...originalAnswer\n              } = questionAnswer;\n              if (Object.keys(originalAnswer).length > 0) {\n                studentAnswer = JSON.stringify(originalAnswer);\n              }\n            }\n            return {\n              questionId: question.id,\n              questionOrder: index + 1,\n              questionSnapshot: JSON.stringify(question),\n              studentAnswer: studentAnswer,\n              correctAnswer: JSON.stringify(((_question$questionDet = question.questionDetails) === null || _question$questionDet === void 0 ? void 0 : _question$questionDet.answer) || question.answer || ''),\n              answerStatus: answerStatus\n            };\n          })\n        };\n        console.log('保存练习结果数据:', practiceData);\n        const response = await LearningAPI.savePracticeResult(practiceData);\n        console.log('练习结果保存成功:', response);\n        setPracticeResultSaved(true);\n        message.success('练习结果已保存', 2);\n\n        // 如果返回了sessionId，保存它以便后续从数据库获取数据\n        if (response !== null && response !== void 0 && response.sessionId && !currentSessionId) {\n          console.log('ResultScreen - 保存sessionId用于数据库查询:', response.sessionId);\n          setCurrentSessionId(response.sessionId);\n        }\n      } catch (error) {\n        console.error('保存练习结果失败:', error);\n        // 不显示错误消息，避免影响用户体验\n      } finally {\n        saveInProgress.current = false;\n      }\n    };\n    savePracticeResult();\n  }, [knowledgePoint === null || knowledgePoint === void 0 ? void 0 : knowledgePoint.id, validQuestions, validUserAnswers, practiceResultSaved]);\n\n  // 处理题目点击\n  const handleQuestionClick = questionIndex => {\n    const question = validQuestions[questionIndex];\n    const questionAnswer = validUserAnswers[question === null || question === void 0 ? void 0 : question.id];\n    if (question) {\n      setSelectedQuestion({\n        question,\n        questionAnswer,\n        index: questionIndex + 1\n      });\n      setModalVisible(true);\n    }\n  };\n\n  // 关闭模态框\n  const handleModalClose = () => {\n    setModalVisible(false);\n    setSelectedQuestion(null);\n  };\n\n  // 处理知识点解析按钮点击\n  const handleKnowledgeAnalysis = async () => {\n    console.log('打开知识点解析弹窗, 知识点:', knowledgePoint);\n    if (!(knowledgePoint !== null && knowledgePoint !== void 0 && knowledgePoint.videoCollectionId)) {\n      message.warning('该知识点暂无解析视频');\n      return;\n    }\n    setLoadingVideos(true);\n    try {\n      // 从API获取知识点对应的视频合集中的视频\n      const response = await LearningAPI.getVideosByCollection(knowledgePoint.videoCollectionId);\n      console.log('获取视频数据成功:', response);\n\n      // 检查API返回的数据结构\n      const videoData = response.data || response;\n      if (videoData && videoData.length > 0) {\n        // 处理视频数据\n        const videos = videoData.map(video => ({\n          id: video.id,\n          title: video.title || `${knowledgePoint.name}解析视频`,\n          videoUrl: video.videoUrl,\n          coverImageUrl: video.coverImageUrl,\n          description: video.description,\n          durationSeconds: video.durationSeconds\n        }));\n        setVideoList(videos);\n        setCurrentVideo(videos[0]); // 设置第一个视频为当前视频\n        setCurrentVideoIndex(0);\n        setKnowledgeAnalysisVisible(true);\n      } else {\n        message.warning('该知识点暂无可用的解析视频');\n      }\n    } catch (error) {\n      console.error('获取知识点视频失败:', error);\n      message.error('获取解析视频失败，请稍后重试');\n    } finally {\n      setLoadingVideos(false);\n    }\n  };\n\n  // 关闭知识点解析弹窗\n  const handleKnowledgeAnalysisClose = () => {\n    setKnowledgeAnalysisVisible(false);\n    setPlaying(false);\n    setCurrentVideo(null);\n    setVideoList([]);\n    setCurrentVideoIndex(0);\n    setLoadingVideos(false);\n  };\n\n  // 切换视频\n  const handleVideoSelect = index => {\n    if (index >= 0 && index < videoList.length) {\n      setCurrentVideoIndex(index);\n      setCurrentVideo(videoList[index]);\n      setPlaying(false); // 切换视频时暂停播放\n    }\n  };\n\n  // 渲染答案内容，支持HTML和数学公式\n  const renderAnswerContent = content => {\n    if (!content) return '';\n    const contentStr = String(content);\n\n    // 检查是否包含HTML标签\n    if (contentStr.includes('<')) {\n      return /*#__PURE__*/_jsxDEV(HtmlContentRenderer, {\n        htmlContent: contentStr,\n        onError: error => console.error('HTML渲染错误:', error)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 检查是否包含LaTeX数学公式\n    if (contentStr.includes('\\\\') || contentStr.includes('{') || contentStr.includes('^') || contentStr.includes('_')) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'left'\n        },\n        children: /*#__PURE__*/_jsxDEV(MathJax, {\n          children: `$${contentStr}$`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 普通文本\n    return contentStr;\n  };\n\n  // 渲染学生答案\n  const renderStudentAnswer = (questionAnswer, question) => {\n    var _question$questionDet3, _question$questionDet4;\n    try {\n      var _question$questionDet2;\n      // 优先使用submittedAnswer字段，如果没有则直接使用questionAnswer对象\n      let submittedAnswer = {};\n      if (questionAnswer.submittedAnswer) {\n        submittedAnswer = JSON.parse(questionAnswer.submittedAnswer);\n      } else {\n        // 从questionAnswer对象中提取答案数据（排除系统字段）\n        const {\n          isCorrect,\n          isSkipped,\n          feedback,\n          ...answerData\n        } = questionAnswer;\n        submittedAnswer = answerData;\n      }\n      const questionType = ((_question$questionDet2 = question.questionDetails) === null || _question$questionDet2 === void 0 ? void 0 : _question$questionDet2.type) || question.questionType;\n      switch (questionType) {\n        case 'SINGLE_CHOICE':\n        case 'MULTIPLE_CHOICE':\n          // 获取用户选择的答案\n          let userAnswer = '';\n          if (submittedAnswer.selectedOptions) {\n            userAnswer = submittedAnswer.selectedOptions[0]; // 单选题取第一个\n          } else if (Object.keys(submittedAnswer).length > 0) {\n            const values = Object.values(submittedAnswer).filter(v => v !== null && v !== undefined);\n            userAnswer = values.length > 0 ? values[0] : '';\n          }\n\n          // 获取题目选项\n          const options = ((_question$questionDet3 = question.questionDetails) === null || _question$questionDet3 === void 0 ? void 0 : _question$questionDet3.options) || question.options;\n          const correctAnswer = ((_question$questionDet4 = question.questionDetails) === null || _question$questionDet4 === void 0 ? void 0 : _question$questionDet4.answer) || question.answer;\n          if (options && Array.isArray(options)) {\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              children: options.map((option, optionIndex) => {\n                const optionLetter = String.fromCharCode(65 + optionIndex); // A, B, C, D\n                const isSelected = userAnswer === optionLetter;\n                const isCorrect = correctAnswer === optionLetter;\n\n                // 确定样式\n                let backgroundColor = 'transparent';\n                let borderColor = '#d9d9d9';\n                let textColor = '#000';\n                let icon = '';\n                let label = '';\n                if (isSelected && isCorrect) {\n                  // 用户选择且正确\n                  backgroundColor = '#f6ffed';\n                  borderColor = '#52c41a';\n                  textColor = '#52c41a';\n                  icon = '✓';\n                  label = '(您的选择 - 正确)';\n                } else if (isSelected && !isCorrect) {\n                  // 用户选择但错误\n                  backgroundColor = '#fff2f0';\n                  borderColor = '#ff4d4f';\n                  textColor = '#ff4d4f';\n                  icon = '✗';\n                  label = '(您的选择 - 错误)';\n                } else if (!isSelected && isCorrect) {\n                  // 正确答案但用户未选择\n                  backgroundColor = '#f6ffed';\n                  borderColor = '#52c41a';\n                  textColor = '#52c41a';\n                  icon = '✓';\n                  label = '(正确答案)';\n                }\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginBottom: '4px',\n                    padding: '8px 12px',\n                    borderRadius: '4px',\n                    backgroundColor,\n                    border: `2px solid ${borderColor}`,\n                    fontWeight: isSelected || isCorrect ? 'bold' : 'normal',\n                    color: textColor\n                  },\n                  children: [icon && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      marginRight: '8px'\n                    },\n                    children: icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 32\n                  }, this), optionLetter, \". \", /*#__PURE__*/_jsxDEV(HtmlContentRenderer, {\n                    htmlContent: option\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 39\n                  }, this), label && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      marginLeft: '8px'\n                    },\n                    children: label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 33\n                  }, this)]\n                }, optionIndex, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this);\n          }\n\n          // 降级处理：如果没有选项数据，显示字母\n          return userAnswer || '未选择';\n        case 'TRUE_FALSE':\n          return submittedAnswer.answer !== undefined ? submittedAnswer.answer ? '正确' : '错误' : '未选择';\n        case 'FILL_IN_BLANK':\n          if (submittedAnswer && typeof submittedAnswer === 'object') {\n            const answers = [];\n            Object.keys(submittedAnswer).sort().forEach(key => {\n              if (key.startsWith('blank_')) {\n                const answerValue = submittedAnswer[key] || '(空)';\n                // 使用renderAnswerContent渲染每个填空答案，支持数学公式\n                answers.push(renderAnswerContent(answerValue));\n              }\n            });\n            if (answers.length > 0) {\n              // 如果只有一个答案，直接返回\n              if (answers.length === 1) {\n                return answers[0];\n              }\n              // 多个答案时，用逗号分隔显示\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                children: answers.map((answer, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [answer, index < answers.length - 1 && ', ']\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 17\n              }, this);\n            }\n            return '未填写';\n          }\n          return '未填写';\n        default:\n          return JSON.stringify(submittedAnswer);\n      }\n    } catch (error) {\n      return '答案解析失败';\n    }\n  };\n\n  // 渲染正确答案\n  const renderCorrectAnswer = question => {\n    try {\n      const questionDetails = question.questionDetails;\n      const questionType = (questionDetails === null || questionDetails === void 0 ? void 0 : questionDetails.type) || question.questionType;\n      const answer = questionDetails === null || questionDetails === void 0 ? void 0 : questionDetails.answer;\n\n      // 处理嵌套题型（阅读理解、听力题等）\n      if (questionType === 'READING_COMPREHENSION' || questionType === 'LISTENING' || questionType === 'CLOZE_TEST') {\n        console.log('renderCorrectAnswer - 处理嵌套题型:', questionType);\n        console.log('questionDetails:', questionDetails);\n\n        // 检查是否有子题目\n        if (questionDetails !== null && questionDetails !== void 0 && questionDetails.subQuestions && Array.isArray(questionDetails.subQuestions)) {\n          const subAnswers = [];\n          questionDetails.subQuestions.forEach((subQuestion, index) => {\n            const subAnswer = subQuestion.answer;\n            if (subAnswer) {\n              subAnswers.push(`第${index + 1}题：${subAnswer}`);\n            } else {\n              subAnswers.push(`第${index + 1}题：答案未设置`);\n            }\n          });\n          return subAnswers.join(', ');\n        }\n\n        // 如果没有子题目但有answer字段，尝试解析\n        if (answer) {\n          if (typeof answer === 'string') {\n            try {\n              const parsedAnswer = JSON.parse(answer);\n              if (typeof parsedAnswer === 'object' && parsedAnswer !== null) {\n                const answerEntries = Object.entries(parsedAnswer);\n                return answerEntries.map(([key, value]) => `${key}：${value}`).join(', ');\n              }\n            } catch (e) {\n              // 解析失败，直接返回字符串\n              return answer;\n            }\n          } else if (typeof answer === 'object' && answer !== null) {\n            const answerEntries = Object.entries(answer);\n            return answerEntries.map(([key, value]) => `${key}：${value}`).join(', ');\n          }\n        }\n        return '答案未设置';\n      }\n      switch (questionType) {\n        case 'SINGLE_CHOICE':\n        case 'MULTIPLE_CHOICE':\n          if (Array.isArray(answer)) {\n            return answer.join(', ');\n          }\n          return answer || '答案未设置';\n        case 'TRUE_FALSE':\n          return answer === true ? '正确' : answer === false ? '错误' : '答案未设置';\n        case 'FILL_IN_BLANK':\n          if (Array.isArray(answer)) {\n            return answer.join(', ');\n          }\n          return answer || '答案未设置';\n        default:\n          return answer ? String(answer) : '答案未设置';\n      }\n    } catch (error) {\n      console.error('renderCorrectAnswer 错误:', error);\n      return '答案解析失败';\n    }\n  };\n\n  // 渲染正确答案（支持HTML）\n  const renderCorrectAnswerWithHtml = question => {\n    try {\n      const questionDetails = question.questionDetails;\n      const questionType = (questionDetails === null || questionDetails === void 0 ? void 0 : questionDetails.type) || question.questionType;\n      const answer = questionDetails === null || questionDetails === void 0 ? void 0 : questionDetails.answer;\n      switch (questionType) {\n        case 'SINGLE_CHOICE':\n        case 'MULTIPLE_CHOICE':\n          // 获取题目选项\n          const options = (questionDetails === null || questionDetails === void 0 ? void 0 : questionDetails.options) || question.options;\n          if (options && Array.isArray(options) && answer) {\n            // 找到正确答案对应的选项内容\n            const answerIndex = answer.charCodeAt(0) - 65; // A=0, B=1, C=2, D=3\n            if (answerIndex >= 0 && answerIndex < options.length) {\n              const correctOptionContent = options[answerIndex];\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [answer, \". \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(HtmlContentRenderer, {\n                  htmlContent: correctOptionContent\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 17\n              }, this);\n            }\n          }\n\n          // 降级处理\n          if (Array.isArray(answer)) {\n            const answerText = answer.join(', ');\n            if (answerText.includes('<')) {\n              return /*#__PURE__*/_jsxDEV(HtmlContentRenderer, {\n                htmlContent: answerText\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 22\n              }, this);\n            }\n            return answerText;\n          }\n          if (answer && typeof answer === 'string' && answer.includes('<')) {\n            return /*#__PURE__*/_jsxDEV(HtmlContentRenderer, {\n              htmlContent: answer\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 20\n            }, this);\n          }\n          return answer || '答案未设置';\n        case 'TRUE_FALSE':\n          return answer === true ? '正确' : answer === false ? '错误' : '答案未设置';\n        case 'FILL_IN_BLANK':\n          if (Array.isArray(answer)) {\n            const answerText = answer.join(', ');\n            // 如果答案包含HTML标签，使用HtmlContentRenderer\n            if (answerText.includes('<')) {\n              return /*#__PURE__*/_jsxDEV(HtmlContentRenderer, {\n                htmlContent: answerText\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 22\n              }, this);\n            }\n            return answerText;\n          }\n          // 如果答案包含HTML标签，使用HtmlContentRenderer\n          if (answer && typeof answer === 'string' && answer.includes('<')) {\n            return /*#__PURE__*/_jsxDEV(HtmlContentRenderer, {\n              htmlContent: answer\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 20\n            }, this);\n          }\n          return answer || '答案未设置';\n        default:\n          if (answer && typeof answer === 'string' && answer.includes('<')) {\n            return /*#__PURE__*/_jsxDEV(HtmlContentRenderer, {\n              htmlContent: answer\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 20\n            }, this);\n          }\n          return answer ? String(answer) : '答案未设置';\n      }\n    } catch (error) {\n      return '答案解析失败';\n    }\n  };\n\n  // 渲染填空题，将学生答案显示在下划线中\n  const renderFillInBlankWithAnswers = useCallback((questionContent, answerData, correctAnswers, questionResult = null) => {\n    try {\n      let content = questionContent;\n      let blankIndex = 0;\n\n      // 将下划线替换为学生答案\n      content = content.replace(/_+/g, () => {\n        const blankId = `blank_${blankIndex}`;\n        const userAnswer = answerData[blankId] || '';\n\n        // 处理正确答案 - 可能是数组格式\n        let correctAnswer = '';\n        if (Array.isArray(correctAnswers)) {\n          correctAnswer = correctAnswers[blankIndex] || '';\n        } else if (correctAnswers[blankId]) {\n          correctAnswer = correctAnswers[blankId];\n        }\n\n        // 🔧 使用后端返回的整体验证结果，而不是前端重复验证\n        // 如果有后端验证结果，使用后端结果；否则降级为简单比较\n        let isCorrect = false;\n        if (questionResult && questionResult.isCorrect !== undefined) {\n          // 使用后端的整体验证结果\n          isCorrect = questionResult.isCorrect;\n        } else {\n          // 降级处理：简单比较答案是否正确\n          isCorrect = userAnswer.trim().toLowerCase() === correctAnswer.trim().toLowerCase();\n        }\n        const statusIcon = isCorrect ? '✓' : '✗';\n        const statusColor = isCorrect ? '#52c41a' : '#ff4d4f';\n        blankIndex++;\n\n        // 如果有答案，显示答案和状态图标\n        if (userAnswer) {\n          return `<span style=\"display: inline-block; min-width: 60px; padding: 2px 8px; border-bottom: 2px solid ${statusColor}; background: transparent; text-align: center; position: relative;\">\n            ${userAnswer} <span style=\"color: ${statusColor}; font-weight: bold; margin-left: 4px;\">${statusIcon}</span>\n          </span>`;\n        } else {\n          // 没有答案，显示空白和错误图标\n          return `<span style=\"display: inline-block; min-width: 60px; padding: 2px 8px; border-bottom: 2px solid #ff4d4f; background: transparent; text-align: center; position: relative;\">\n            <span style=\"color: #999;\">(空)</span> <span style=\"color: #ff4d4f; font-weight: bold; margin-left: 4px;\">✗</span>\n          </span>`;\n        }\n      });\n      return /*#__PURE__*/_jsxDEV(HtmlContentRenderer, {\n        htmlContent: content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 648,\n        columnNumber: 14\n      }, this);\n    } catch (error) {\n      console.error('渲染填空题答案失败:', error);\n      return '渲染失败';\n    }\n  }, []);\n\n  // 如果正在加载数据库数据，显示加载状态\n  if (loadingDbData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"scene-container result-screen\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '18px',\n            marginBottom: '20px',\n            textAlign: 'center'\n          },\n          children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u7EC3\\u4E60\\u7ED3\\u679C...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 660,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-lg\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 659,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 658,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 计算统计数据\n  const hasQuestions = validQuestions.length > 0;\n\n  // 简单的分数计算逻辑 - 基于已答题目的正确率\n  const calculateSimpleScore = (questions, userAnswers) => {\n    if (!questions.length) {\n      return {\n        correctCount: 0,\n        totalCount: 0,\n        answeredCount: 0,\n        accuracy: 0\n      };\n    }\n    let correctCount = 0;\n    let answeredCount = 0;\n    questions.forEach(question => {\n      const userAnswer = userAnswers[question.id];\n      if (userAnswer) {\n        answeredCount++;\n        if (userAnswer.isCorrect) {\n          correctCount++;\n        }\n      }\n    });\n    return {\n      correctCount,\n      totalCount: questions.length,\n      answeredCount,\n      // 基于已答题目的正确率：答对的题目数 / 已答题目数\n      accuracy: answeredCount > 0 ? correctCount / answeredCount : 0\n    };\n  };\n  const scoreResult = calculateSimpleScore(validQuestions, validUserAnswers);\n\n  // 兼容旧的stats格式\n  const stats = {\n    completionRate: scoreResult.totalCount > 0 ? scoreResult.answeredCount / scoreResult.totalCount * 100 : 0,\n    correctRate: scoreResult.accuracy || 0,\n    answeredQuestions: scoreResult.answeredCount || 0,\n    totalQuestions: scoreResult.totalCount || 0\n  };\n\n  // 计算掌握状态\n  const masteryLevel = stats.correctRate >= 0.8 ? '已掌握' : stats.correctRate >= 0.6 ? '基本掌握' : '未掌握';\n  const masteryIcon = stats.correctRate >= 0.8 ? '😊' : stats.correctRate >= 0.6 ? '😐' : '😞';\n  const masteryColor = stats.correctRate >= 0.8 ? '#52c41a' : stats.correctRate >= 0.6 ? '#faad14' : '#ff4d4f';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"scene-container result-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"result-main-content\",\n      style: {\n        backgroundImage: `url(${process.env.PUBLIC_URL}/images/bg1.png)`,\n        backgroundSize: 'cover',\n        backgroundPosition: 'center',\n        backgroundRepeat: 'no-repeat'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"result-card-optimized\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"result-header-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"result-avatar\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '48px'\n              },\n              children: masteryIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Title, {\n            level: 3,\n            className: \"result-title\",\n            children: masteryLevel\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 732,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 728,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"result-knowledge-section\",\n          children: /*#__PURE__*/_jsxDEV(Title, {\n            level: 2,\n            className: \"knowledge-point-name\",\n            children: (effectiveKnowledgePoint === null || effectiveKnowledgePoint === void 0 ? void 0 : effectiveKnowledgePoint.name) || '知识点'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 739,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 738,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"result-progress-bar\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-container\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-fill\",\n              style: {\n                width: `${Math.round(stats.correctRate * 100)}%`,\n                backgroundColor: masteryColor\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"progress-text\",\n            children: [Math.round(stats.correctRate * 100), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 744,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 726,\n        columnNumber: 9\n      }, this), isHistoryView && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          margin: '20px 0',\n          color: '#666',\n          fontSize: '14px'\n        },\n        children: \"\\uD83D\\uDCDA \\u8FD9\\u662F\\u60A8\\u7684\\u5386\\u53F2\\u7EC3\\u4E60\\u8BB0\\u5F55\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 760,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          margin: '20px 0'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"knowledge-analysis-button\",\n          style: {\n            background: 'linear-gradient(135deg, #FF8A4C 0%, #F17A31 100%)',\n            border: '2px solid #D16428',\n            color: '#FFFFFF',\n            fontWeight: '600',\n            borderRadius: '24px',\n            padding: '8px 24px',\n            height: '40px',\n            fontSize: '14px',\n            boxShadow: '0 4px 12px rgba(241, 122, 49, 0.3)',\n            transition: 'all 0.2s ease'\n          },\n          onMouseEnter: e => {\n            e.target.style.background = 'linear-gradient(135deg, #FF9A5C 0%, #E16A28 100%)';\n            e.target.style.transform = 'translateY(-2px)';\n            e.target.style.boxShadow = '0 6px 16px rgba(241, 122, 49, 0.4)';\n          },\n          onMouseLeave: e => {\n            e.target.style.background = 'linear-gradient(135deg, #FF8A4C 0%, #F17A31 100%)';\n            e.target.style.transform = 'translateY(0)';\n            e.target.style.boxShadow = '0 4px 12px rgba(241, 122, 49, 0.3)';\n          },\n          onClick: handleKnowledgeAnalysis,\n          children: \"\\u77E5\\u8BC6\\u70B9\\u89E3\\u6790\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 767,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 766,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"result-actions-area\",\n        children: [hasQuestions && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"wrong-questions-summary\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"summary-label\",\n            children: \"\\u67E5\\u770B\\u9898\\u76EE\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 802,\n            columnNumber: 15\n          }, this), validQuestions.map((_, index) => {\n            var _validQuestions$index;\n            const questionAnswer = validUserAnswers[(_validQuestions$index = validQuestions[index]) === null || _validQuestions$index === void 0 ? void 0 : _validQuestions$index.id];\n            const isCorrect = questionAnswer === null || questionAnswer === void 0 ? void 0 : questionAnswer.isCorrect;\n            const isSkipped = questionAnswer === null || questionAnswer === void 0 ? void 0 : questionAnswer.isSkipped;\n            return /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `result-question-number ${isSkipped ? 'skipped' : isCorrect ? 'correct' : 'wrong'}`,\n              onClick: () => handleQuestionClick(index),\n              style: {\n                cursor: 'pointer'\n              },\n              title: \"\\u70B9\\u51FB\\u67E5\\u770B\\u9898\\u76EE\\u8BE6\\u60C5\",\n              children: index + 1\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 19\n            }, this);\n          })]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 801,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"result-button-group\",\n          children: [onRestart && /*#__PURE__*/_jsxDEV(Button, {\n            className: \"restart-button\",\n            onClick: onRestart,\n            style: {\n              background: '#FFFFFF',\n              border: '2px solid #FF8A4C',\n              color: '#FF8A4C',\n              fontWeight: '600',\n              borderRadius: '24px',\n              padding: '8px 24px',\n              height: '40px',\n              fontSize: '14px',\n              width: '120px',\n              transition: 'all 0.2s ease'\n            },\n            onMouseEnter: e => {\n              e.target.style.background = '#FFF5F0';\n              e.target.style.transform = 'translateY(-2px)';\n              e.target.style.boxShadow = '0 4px 12px rgba(255, 138, 76, 0.3)';\n            },\n            onMouseLeave: e => {\n              e.target.style.background = '#FFFFFF';\n              e.target.style.transform = 'translateY(0)';\n              e.target.style.boxShadow = 'none';\n            },\n            children: \"\\u518D\\u6765\\u4E00\\u6B21\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            size: \"large\",\n            className: \"primary-button\",\n            onClick: () => window.history.back(),\n            children: \"\\u8FD4\\u56DE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 856,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 825,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 798,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 716,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(QuestionDetailModal, {\n      visible: modalVisible,\n      onClose: handleModalClose,\n      selectedQuestion: selectedQuestion,\n      renderStudentAnswer: renderStudentAnswer,\n      renderCorrectAnswer: renderCorrectAnswerWithHtml,\n      renderFillInBlankWithAnswers: renderFillInBlankWithAnswers\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 869,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: null,\n      open: knowledgeAnalysisVisible,\n      onCancel: handleKnowledgeAnalysisClose,\n      footer: null,\n      width: 1200,\n      centered: true,\n      closable: false,\n      className: \"knowledge-analysis-modal\",\n      styles: {\n        body: {\n          padding: 0\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"task-kpoint__exp__tabs\",\n        style: {\n          background: 'rgba(0, 0, 0, 0.8)',\n          padding: '0',\n          position: 'relative',\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '100%',\n            padding: '12px 20px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'flex-start',\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"task-kpoint__exp__tab task-kpoint__exp__tab_active\",\n            style: {\n              background: '#ff6b35',\n              color: '#ffffff',\n              padding: '8px 16px',\n              borderRadius: '6px 6px 0 0',\n              fontSize: '14px',\n              fontWeight: '600',\n              marginRight: 'auto'\n            },\n            children: \"\\u77E5\\u8BC6\\u70B9\\u89C6\\u9891\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 907,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"task-kpoint__exp__close\",\n            onClick: handleKnowledgeAnalysisClose,\n            style: {\n              cursor: 'pointer',\n              color: '#ffffff',\n              fontSize: '16px',\n              padding: '4px',\n              position: 'absolute',\n              right: '20px',\n              top: '50%',\n              transform: 'translateY(-50%)'\n            },\n            children: /*#__PURE__*/_jsxDEV(CloseOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 928,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 918,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 899,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 893,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-analysis-container\",\n        style: {\n          display: 'flex',\n          height: '600px',\n          background: '#ffffff'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '80%',\n            display: 'flex',\n            flexDirection: 'column',\n            background: '#ffffff',\n            position: 'relative'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1,\n              position: 'relative',\n              minHeight: '500px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: [loadingVideos ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#ffffff',\n                fontSize: '16px',\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(PlayCircleOutlined, {\n                style: {\n                  fontSize: '48px',\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 960,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u89C6\\u9891...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 961,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 955,\n              columnNumber: 17\n            }, this) : currentVideo ? /*#__PURE__*/_jsxDEV(ReactPlayer, {\n              url: currentVideo.videoUrl,\n              width: \"100%\",\n              height: \"100%\",\n              playing: playing,\n              playbackRate: playbackRate,\n              controls: true,\n              light: currentVideo.coverImageUrl,\n              onPlay: () => setPlaying(true),\n              onPause: () => setPlaying(false),\n              onError: error => {\n                console.error('视频播放错误:', error);\n                message.error('视频播放失败');\n              },\n              config: {\n                file: {\n                  attributes: {\n                    style: {\n                      width: '100%',\n                      height: '100%'\n                    },\n                    controlsList: 'nodownload',\n                    disablePictureInPicture: false\n                  }\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 964,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#ffffff',\n                fontSize: '16px',\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(PlayCircleOutlined, {\n                style: {\n                  fontSize: '48px',\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 994,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"\\u6682\\u65E0\\u89C6\\u9891\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 995,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 989,\n              columnNumber: 17\n            }, this), currentVideo && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                bottom: '10px',\n                right: '10px',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                background: 'rgba(0, 0, 0, 0.7)',\n                padding: '6px 12px',\n                borderRadius: '20px',\n                color: '#ffffff',\n                fontSize: '12px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u901F\\u5EA6:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1014,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: playbackRate,\n                onChange: e => setPlaybackRate(parseFloat(e.target.value)),\n                style: {\n                  background: 'transparent',\n                  border: 'none',\n                  color: '#ffffff',\n                  fontSize: '12px',\n                  cursor: 'pointer',\n                  outline: 'none'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 0.5,\n                  style: {\n                    background: '#333',\n                    color: '#fff'\n                  },\n                  children: \"0.5x\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1027,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 0.75,\n                  style: {\n                    background: '#333',\n                    color: '#fff'\n                  },\n                  children: \"0.75x\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1028,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 1,\n                  style: {\n                    background: '#333',\n                    color: '#fff'\n                  },\n                  children: \"1x\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1029,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 1.25,\n                  style: {\n                    background: '#333',\n                    color: '#fff'\n                  },\n                  children: \"1.25x\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1030,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 1.5,\n                  style: {\n                    background: '#333',\n                    color: '#fff'\n                  },\n                  children: \"1.5x\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1031,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 2,\n                  style: {\n                    background: '#333',\n                    color: '#fff'\n                  },\n                  children: \"2x\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1032,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1015,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1001,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 946,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 939,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '20%',\n            background: '#2f2f2f',\n            padding: '20px',\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                margin: '0 0 16px 0',\n                color: '#ffffff',\n                fontSize: '16px',\n                fontWeight: '600'\n              },\n              children: \"\\u76F8\\u5173\\u77E5\\u8BC6\\u70B9\\u89C6\\u9891\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1049,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1048,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-list-container\",\n            style: {\n              flex: 1,\n              overflowY: 'auto'\n            },\n            children: videoList.map((video, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: () => handleVideoSelect(index),\n              className: `video-list-item ${currentVideoIndex === index ? 'active' : ''}`,\n              style: {\n                padding: '12px',\n                margin: '0 0 8px 0',\n                background: currentVideoIndex === index ? '#404040' : 'transparent',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                transition: 'all 0.2s',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '12px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: '#ff6b35',\n                  color: '#ffffff',\n                  fontSize: '12px',\n                  fontWeight: '600',\n                  padding: '4px 8px',\n                  borderRadius: '4px',\n                  minWidth: '24px',\n                  textAlign: 'center'\n                },\n                children: index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1079,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  flex: 1,\n                  minWidth: 0\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: '500',\n                    color: '#ffffff',\n                    fontSize: '13px',\n                    marginBottom: '6px',\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap'\n                  },\n                  children: video.title.replace(/_\\d{14}$/, '')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1094,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '12px',\n                    fontSize: '11px',\n                    color: '#cccccc'\n                  },\n                  children: video.durationSeconds && /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [Math.floor(video.durationSeconds / 60).toString().padStart(2, '0'), \":\", (video.durationSeconds % 60).toString().padStart(2, '0')]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1113,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1105,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1093,\n                columnNumber: 19\n              }, this)]\n            }, video.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1062,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1060,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1040,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 933,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 879,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 714,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultScreen, \"+wMSwQ1Cj4tIIU+Vl0zrDMveSoI=\");\n_c = ResultScreen;\nResultScreen.propTypes = {\n  knowledgePoint: PropTypes.shape({\n    id: PropTypes.number,\n    name: PropTypes.string,\n    chapterName: PropTypes.string,\n    subjectName: PropTypes.string\n  }),\n  progress: PropTypes.number,\n  userAnswers: PropTypes.object,\n  questions: PropTypes.array,\n  onRestart: PropTypes.func,\n  onViewQuestions: PropTypes.func,\n  sessionId: PropTypes.number,\n  // 可选的练习会话ID\n  testStartTime: PropTypes.number,\n  // 测试开始时间戳\n  testEndTime: PropTypes.number // 测试结束时间戳\n};\nexport default ResultScreen;\nvar _c;\n$RefreshReg$(_c, \"ResultScreen\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "useCallback", "Typography", "<PERSON><PERSON>", "Modal", "Tag", "message", "CheckCircleOutlined", "RightOutlined", "CloseCircleOutlined", "ExclamationCircleOutlined", "PlayCircleOutlined", "CloseOutlined", "ReactPlayer", "PropTypes", "HtmlContent<PERSON>enderer", "ContentBlockList", "LearningAPI", "MathJax", "QuestionDetailModal", "jsxDEV", "_jsxDEV", "Title", "ResultScreen", "knowledgePoint", "progress", "userAnswers", "questions", "onRestart", "onViewQuestions", "sessionId", "isHistoryView", "testStartTime", "testEndTime", "_s", "console", "log", "name", "questionsCount", "length", "userAnswersCount", "Object", "keys", "selectedQuestion", "setSelectedQuestion", "modalVisible", "setModalVisible", "practiceResultSaved", "setPracticeResultSaved", "dbData", "setDbData", "loadingDbData", "setLoadingDbData", "currentSessionId", "setCurrentSessionId", "knowledgeAnalysisVisible", "setKnowledgeAnalysisVisible", "currentVideo", "setCurrentVideo", "playing", "setPlaying", "playbackRate", "setPlaybackRate", "videoList", "setVideoList", "currentVideoIndex", "setCurrentVideoIndex", "loadingVideos", "setLoadingVideos", "hasDbData", "saveInProgress", "fetchPracticeData", "practiceDetail", "getPracticeDetail", "convertedData", "convertPracticeDetailToProps", "warn", "error", "hasSessionId", "isLoading", "effectiveKnowledgePoint", "effectiveQuestions", "effectiveUserAnswers", "validQuestions", "Array", "isArray", "validUserAnswers", "savePracticeResult", "current", "id", "hasKnowledgePoint", "actualDurationSeconds", "Math", "floor", "practiceData", "knowledgePointId", "sessionType", "durationSeconds", "map", "question", "index", "_question$questionDet", "questionAnswer", "answerStatus", "isSkipped", "isCorrect", "studentAnswer", "feedback", "originalAnswer", "JSON", "stringify", "questionId", "questionOrder", "questionSnapshot", "<PERSON><PERSON><PERSON><PERSON>", "questionDetails", "answer", "response", "success", "handleQuestionClick", "questionIndex", "handleModalClose", "handleKnowledgeAnalysis", "videoCollectionId", "warning", "getVideosByCollection", "videoData", "data", "videos", "video", "title", "videoUrl", "coverImageUrl", "description", "handleKnowledgeAnalysisClose", "handleVideoSelect", "renderAnswerContent", "content", "contentStr", "String", "includes", "htmlContent", "onError", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "textAlign", "children", "renderStudentAnswer", "_question$questionDet3", "_question$questionDet4", "_question$questionDet2", "submittedAnswer", "parse", "answerData", "questionType", "type", "userAnswer", "selectedOptions", "values", "filter", "v", "undefined", "options", "option", "optionIndex", "optionLetter", "fromCharCode", "isSelected", "backgroundColor", "borderColor", "textColor", "icon", "label", "marginBottom", "padding", "borderRadius", "border", "fontWeight", "color", "marginRight", "marginLeft", "answers", "sort", "for<PERSON>ach", "key", "startsWith", "answerValue", "push", "renderCorrectAnswer", "subQuestions", "subAnswers", "subQuestion", "subAnswer", "join", "parsedAnswer", "answerEntries", "entries", "value", "e", "renderCorrectAnswerWithHtml", "answerIndex", "charCodeAt", "correctOptionContent", "answerText", "renderFillInBlankWithAnswers", "questionContent", "correctAnswers", "questionResult", "blankIndex", "replace", "blankId", "trim", "toLowerCase", "statusIcon", "statusColor", "className", "fontSize", "hasQuestions", "calculateSimpleScore", "correctCount", "totalCount", "answeredCount", "accuracy", "scoreResult", "stats", "completionRate", "correctRate", "answeredQuestions", "totalQuestions", "masteryLevel", "masteryIcon", "masteryColor", "backgroundImage", "process", "env", "PUBLIC_URL", "backgroundSize", "backgroundPosition", "backgroundRepeat", "level", "width", "round", "margin", "background", "height", "boxShadow", "transition", "onMouseEnter", "target", "transform", "onMouseLeave", "onClick", "_", "_validQuestions$index", "cursor", "size", "window", "history", "back", "visible", "onClose", "open", "onCancel", "footer", "centered", "closable", "styles", "body", "position", "display", "alignItems", "justifyContent", "right", "top", "flexDirection", "flex", "minHeight", "url", "controls", "light", "onPlay", "onPause", "config", "file", "attributes", "controlsList", "disablePictureInPicture", "bottom", "gap", "onChange", "parseFloat", "outline", "overflowY", "min<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "whiteSpace", "toString", "padStart", "_c", "propTypes", "shape", "number", "string", "chapterName", "subjectName", "object", "array", "func", "$RefreshReg$"], "sources": ["D:/projects/AIstrusys/frontend/learning/src/components/LearningModule/scenes/ResultScreen.js"], "sourcesContent": ["import { useState, useEffect, useRef, useCallback } from 'react';\r\nimport { Typography, Button, Modal, Tag, message } from 'antd';\r\nimport { CheckCircleOutlined, RightOutlined, CloseCircleOutlined, ExclamationCircleOutlined, PlayCircleOutlined, CloseOutlined } from '@ant-design/icons';\r\nimport ReactPlayer from 'react-player';\r\nimport PropTypes from 'prop-types';\r\nimport '../../../styles/SceneStyles.css';\r\nimport './ResultScreen.css';\r\nimport HtmlContentRenderer from '../../content/HtmlContentRenderer';\r\nimport { ContentBlockList } from '../../content/ContentBlockRenderer';\r\nimport { LearningAPI } from '../../../api/learning';\r\nimport { MathJax } from 'better-react-mathjax';\r\nimport QuestionDetailModal from '../../common/QuestionDetailModal';\r\n\r\nconst { Title } = Typography;\r\n\r\n/**\r\n * 结果场景组件\r\n *\r\n * @param {Object} props 组件属性\r\n * @param {Object} props.knowledgePoint 知识点数据\r\n * @param {number} props.progress 学习进度\r\n * @param {Object} props.userAnswers 用户答案\r\n * @param {Array} props.questions 题目列表\r\n * @param {Function} props.onRestart 重新开始学习的回调函数\r\n * @param {Function} props.onViewQuestions 查看题目的回调函数\r\n * @param {number} props.sessionId 可选的练习会话ID，用于从数据库获取数据\r\n * @param {number} props.testStartTime 测试开始时间戳\r\n * @param {number} props.testEndTime 测试结束时间戳\r\n * @returns {JSX.Element} 结果场景组件\r\n */\r\nconst ResultScreen = ({ knowledgePoint, progress, userAnswers = {}, questions = [], onRestart, onViewQuestions, sessionId, isHistoryView = false, testStartTime, testEndTime }) => {\r\n  console.log('ResultScreen - 组件初始化, props:', {\r\n    knowledgePoint: knowledgePoint?.name,\r\n    questionsCount: questions.length,\r\n    userAnswersCount: Object.keys(userAnswers).length,\r\n    sessionId: sessionId\r\n  });\r\n\r\n  // 状态管理\r\n  const [selectedQuestion, setSelectedQuestion] = useState(null);\r\n  const [modalVisible, setModalVisible] = useState(false);\r\n  const [practiceResultSaved, setPracticeResultSaved] = useState(false);\r\n  const [dbData, setDbData] = useState(null);\r\n  const [loadingDbData, setLoadingDbData] = useState(false);\r\n  const [currentSessionId, setCurrentSessionId] = useState(sessionId);\r\n\r\n  // 知识点解析弹窗状态\r\n  const [knowledgeAnalysisVisible, setKnowledgeAnalysisVisible] = useState(false);\r\n  const [currentVideo, setCurrentVideo] = useState(null);\r\n  const [playing, setPlaying] = useState(false);\r\n  const [playbackRate, setPlaybackRate] = useState(1);\r\n  const [videoList, setVideoList] = useState([]);\r\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\r\n  const [loadingVideos, setLoadingVideos] = useState(false);\r\n\r\n  console.log('ResultScreen - 初始状态:', {\r\n    currentSessionId,\r\n    hasDbData: !!dbData,\r\n    loadingDbData\r\n  });\r\n\r\n  // 使用ref防止重复保存\r\n  const saveInProgress = useRef(false);\r\n\r\n  // 从数据库获取练习数据\r\n  useEffect(() => {\r\n    console.log('ResultScreen - useEffect触发, currentSessionId:', currentSessionId, 'dbData:', !!dbData, 'loadingDbData:', loadingDbData);\r\n\r\n    const fetchPracticeData = async () => {\r\n      // 如果提供了sessionId，从数据库获取数据\r\n      if (currentSessionId && !dbData && !loadingDbData) {\r\n        console.log('ResultScreen - 开始从数据库获取练习数据, sessionId:', currentSessionId);\r\n        setLoadingDbData(true);\r\n\r\n        try {\r\n          console.log('ResultScreen - 调用getPracticeDetail API');\r\n          const practiceDetail = await LearningAPI.getPracticeDetail(currentSessionId);\r\n          console.log('ResultScreen - getPracticeDetail API响应:', practiceDetail);\r\n\r\n          const convertedData = await LearningAPI.convertPracticeDetailToProps(practiceDetail);\r\n          console.log('ResultScreen - 数据转换结果:', convertedData);\r\n\r\n          if (convertedData) {\r\n            console.log('ResultScreen - 数据库数据获取成功，设置dbData');\r\n            setDbData(convertedData);\r\n          } else {\r\n            console.warn('ResultScreen - 数据库数据转换失败');\r\n          }\r\n        } catch (error) {\r\n          console.error('ResultScreen - 获取数据库数据失败:', error);\r\n        } finally {\r\n          console.log('ResultScreen - 数据获取完成，设置loadingDbData为false');\r\n          setLoadingDbData(false);\r\n        }\r\n      } else {\r\n        console.log('ResultScreen - 跳过数据获取，原因:', {\r\n          hasSessionId: !!currentSessionId,\r\n          hasDbData: !!dbData,\r\n          isLoading: loadingDbData\r\n        });\r\n      }\r\n    };\r\n\r\n    fetchPracticeData();\r\n  }, [currentSessionId, dbData, loadingDbData]);\r\n\r\n  // 确定数据来源：优先使用数据库数据，然后是props数据\r\n  const effectiveKnowledgePoint = dbData?.knowledgePoint || knowledgePoint;\r\n  const effectiveQuestions = dbData?.questions || questions;\r\n  const effectiveUserAnswers = dbData?.userAnswers || userAnswers;\r\n\r\n  // 确保questions是数组，userAnswers是对象\r\n  const validQuestions = Array.isArray(effectiveQuestions) ? effectiveQuestions : [];\r\n  const validUserAnswers = effectiveUserAnswers && typeof effectiveUserAnswers === 'object' ? effectiveUserAnswers : {};\r\n\r\n  // 保存练习结果\r\n  useEffect(() => {\r\n    const savePracticeResult = async () => {\r\n      // 避免重复保存\r\n      if (practiceResultSaved || saveInProgress.current || !knowledgePoint?.id || validQuestions.length === 0) {\r\n        console.log('ResultScreen - 跳过保存:', {\r\n          practiceResultSaved,\r\n          saveInProgress: saveInProgress.current,\r\n          hasKnowledgePoint: !!knowledgePoint?.id,\r\n          questionsCount: validQuestions.length\r\n        });\r\n        return;\r\n      }\r\n\r\n      console.log('ResultScreen - 开始保存练习结果');\r\n      saveInProgress.current = true;\r\n\r\n      try {\r\n        // 计算实际测试时长\r\n        const actualDurationSeconds = testStartTime && testEndTime ?\r\n          Math.floor((testEndTime - testStartTime) / 1000) :\r\n          validQuestions.length * 60; // 如果没有时间记录，按每题1分钟估算\r\n\r\n        // 构建练习数据\r\n        const practiceData = {\r\n          knowledgePointId: knowledgePoint.id,\r\n          sessionType: 'KNOWLEDGE_POINT_PRACTICE',\r\n          durationSeconds: actualDurationSeconds, // 添加实际测试时长\r\n          questions: validQuestions.map((question, index) => {\r\n            const questionAnswer = validUserAnswers[question.id];\r\n\r\n            // 确定答题状态\r\n            let answerStatus = 'NOT_ANSWERED';\r\n            if (questionAnswer) {\r\n              if (questionAnswer.isSkipped) {\r\n                answerStatus = 'SKIPPED';\r\n              } else if (questionAnswer.isCorrect) {\r\n                answerStatus = 'CORRECT';\r\n              } else {\r\n                answerStatus = 'INCORRECT';\r\n              }\r\n            }\r\n\r\n            // 获取学生的真实输入答案\r\n            let studentAnswer = null;\r\n            if (questionAnswer && !questionAnswer.isSkipped) {\r\n              // 提取学生的原始输入数据（排除系统添加的字段）\r\n              const { isCorrect, isSkipped, feedback, ...originalAnswer } = questionAnswer;\r\n              if (Object.keys(originalAnswer).length > 0) {\r\n                studentAnswer = JSON.stringify(originalAnswer);\r\n              }\r\n            }\r\n\r\n            return {\r\n              questionId: question.id,\r\n              questionOrder: index + 1,\r\n              questionSnapshot: JSON.stringify(question),\r\n              studentAnswer: studentAnswer,\r\n              correctAnswer: JSON.stringify(question.questionDetails?.answer || question.answer || ''),\r\n              answerStatus: answerStatus\r\n            };\r\n          })\r\n        };\r\n\r\n        console.log('保存练习结果数据:', practiceData);\r\n\r\n        const response = await LearningAPI.savePracticeResult(practiceData);\r\n        console.log('练习结果保存成功:', response);\r\n\r\n        setPracticeResultSaved(true);\r\n        message.success('练习结果已保存', 2);\r\n\r\n        // 如果返回了sessionId，保存它以便后续从数据库获取数据\r\n        if (response?.sessionId && !currentSessionId) {\r\n          console.log('ResultScreen - 保存sessionId用于数据库查询:', response.sessionId);\r\n          setCurrentSessionId(response.sessionId);\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('保存练习结果失败:', error);\r\n        // 不显示错误消息，避免影响用户体验\r\n      } finally {\r\n        saveInProgress.current = false;\r\n      }\r\n    };\r\n\r\n    savePracticeResult();\r\n  }, [knowledgePoint?.id, validQuestions, validUserAnswers, practiceResultSaved]);\r\n  \r\n  // 处理题目点击\r\n  const handleQuestionClick = (questionIndex) => {\r\n    const question = validQuestions[questionIndex];\r\n    const questionAnswer = validUserAnswers[question?.id];\r\n\r\n    if (question) {\r\n      setSelectedQuestion({\r\n        question,\r\n        questionAnswer,\r\n        index: questionIndex + 1\r\n      });\r\n      setModalVisible(true);\r\n    }\r\n  };\r\n\r\n  // 关闭模态框\r\n  const handleModalClose = () => {\r\n    setModalVisible(false);\r\n    setSelectedQuestion(null);\r\n  };\r\n\r\n  // 处理知识点解析按钮点击\r\n  const handleKnowledgeAnalysis = async () => {\r\n    console.log('打开知识点解析弹窗, 知识点:', knowledgePoint);\r\n\r\n    if (!knowledgePoint?.videoCollectionId) {\r\n      message.warning('该知识点暂无解析视频');\r\n      return;\r\n    }\r\n\r\n    setLoadingVideos(true);\r\n\r\n    try {\r\n      // 从API获取知识点对应的视频合集中的视频\r\n      const response = await LearningAPI.getVideosByCollection(knowledgePoint.videoCollectionId);\r\n      console.log('获取视频数据成功:', response);\r\n\r\n      // 检查API返回的数据结构\r\n      const videoData = response.data || response;\r\n      if (videoData && videoData.length > 0) {\r\n        // 处理视频数据\r\n        const videos = videoData.map(video => ({\r\n          id: video.id,\r\n          title: video.title || `${knowledgePoint.name}解析视频`,\r\n          videoUrl: video.videoUrl,\r\n          coverImageUrl: video.coverImageUrl,\r\n          description: video.description,\r\n          durationSeconds: video.durationSeconds\r\n        }));\r\n\r\n        setVideoList(videos);\r\n        setCurrentVideo(videos[0]); // 设置第一个视频为当前视频\r\n        setCurrentVideoIndex(0);\r\n        setKnowledgeAnalysisVisible(true);\r\n      } else {\r\n        message.warning('该知识点暂无可用的解析视频');\r\n      }\r\n    } catch (error) {\r\n      console.error('获取知识点视频失败:', error);\r\n      message.error('获取解析视频失败，请稍后重试');\r\n    } finally {\r\n      setLoadingVideos(false);\r\n    }\r\n  };\r\n\r\n  // 关闭知识点解析弹窗\r\n  const handleKnowledgeAnalysisClose = () => {\r\n    setKnowledgeAnalysisVisible(false);\r\n    setPlaying(false);\r\n    setCurrentVideo(null);\r\n    setVideoList([]);\r\n    setCurrentVideoIndex(0);\r\n    setLoadingVideos(false);\r\n  };\r\n\r\n  // 切换视频\r\n  const handleVideoSelect = (index) => {\r\n    if (index >= 0 && index < videoList.length) {\r\n      setCurrentVideoIndex(index);\r\n      setCurrentVideo(videoList[index]);\r\n      setPlaying(false); // 切换视频时暂停播放\r\n    }\r\n  };\r\n\r\n  // 渲染答案内容，支持HTML和数学公式\r\n  const renderAnswerContent = (content) => {\r\n    if (!content) return '';\r\n\r\n    const contentStr = String(content);\r\n\r\n    // 检查是否包含HTML标签\r\n    if (contentStr.includes('<')) {\r\n      return (\r\n        <HtmlContentRenderer\r\n          htmlContent={contentStr}\r\n          onError={(error) => console.error('HTML渲染错误:', error)}\r\n        />\r\n      );\r\n    }\r\n\r\n    // 检查是否包含LaTeX数学公式\r\n    if (contentStr.includes('\\\\') || contentStr.includes('{') || contentStr.includes('^') || contentStr.includes('_')) {\r\n      return (\r\n        <div style={{ textAlign: 'left' }}>\r\n          <MathJax>\r\n            {`$${contentStr}$`}\r\n          </MathJax>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    // 普通文本\r\n    return contentStr;\r\n  };\r\n\r\n  // 渲染学生答案\r\n  const renderStudentAnswer = (questionAnswer, question) => {\r\n    try {\r\n      // 优先使用submittedAnswer字段，如果没有则直接使用questionAnswer对象\r\n      let submittedAnswer = {};\r\n      if (questionAnswer.submittedAnswer) {\r\n        submittedAnswer = JSON.parse(questionAnswer.submittedAnswer);\r\n      } else {\r\n        // 从questionAnswer对象中提取答案数据（排除系统字段）\r\n        const { isCorrect, isSkipped, feedback, ...answerData } = questionAnswer;\r\n        submittedAnswer = answerData;\r\n      }\r\n\r\n      const questionType = question.questionDetails?.type || question.questionType;\r\n\r\n      switch (questionType) {\r\n        case 'SINGLE_CHOICE':\r\n        case 'MULTIPLE_CHOICE':\r\n          // 获取用户选择的答案\r\n          let userAnswer = '';\r\n          if (submittedAnswer.selectedOptions) {\r\n            userAnswer = submittedAnswer.selectedOptions[0]; // 单选题取第一个\r\n          } else if (Object.keys(submittedAnswer).length > 0) {\r\n            const values = Object.values(submittedAnswer).filter(v => v !== null && v !== undefined);\r\n            userAnswer = values.length > 0 ? values[0] : '';\r\n          }\r\n\r\n          // 获取题目选项\r\n          const options = question.questionDetails?.options || question.options;\r\n          const correctAnswer = question.questionDetails?.answer || question.answer;\r\n\r\n          if (options && Array.isArray(options)) {\r\n            return (\r\n              <div>\r\n                {options.map((option, optionIndex) => {\r\n                  const optionLetter = String.fromCharCode(65 + optionIndex); // A, B, C, D\r\n                  const isSelected = userAnswer === optionLetter;\r\n                  const isCorrect = correctAnswer === optionLetter;\r\n\r\n                  // 确定样式\r\n                  let backgroundColor = 'transparent';\r\n                  let borderColor = '#d9d9d9';\r\n                  let textColor = '#000';\r\n                  let icon = '';\r\n                  let label = '';\r\n\r\n                  if (isSelected && isCorrect) {\r\n                    // 用户选择且正确\r\n                    backgroundColor = '#f6ffed';\r\n                    borderColor = '#52c41a';\r\n                    textColor = '#52c41a';\r\n                    icon = '✓';\r\n                    label = '(您的选择 - 正确)';\r\n                  } else if (isSelected && !isCorrect) {\r\n                    // 用户选择但错误\r\n                    backgroundColor = '#fff2f0';\r\n                    borderColor = '#ff4d4f';\r\n                    textColor = '#ff4d4f';\r\n                    icon = '✗';\r\n                    label = '(您的选择 - 错误)';\r\n                  } else if (!isSelected && isCorrect) {\r\n                    // 正确答案但用户未选择\r\n                    backgroundColor = '#f6ffed';\r\n                    borderColor = '#52c41a';\r\n                    textColor = '#52c41a';\r\n                    icon = '✓';\r\n                    label = '(正确答案)';\r\n                  }\r\n\r\n                  return (\r\n                    <div\r\n                      key={optionIndex}\r\n                      style={{\r\n                        marginBottom: '4px',\r\n                        padding: '8px 12px',\r\n                        borderRadius: '4px',\r\n                        backgroundColor,\r\n                        border: `2px solid ${borderColor}`,\r\n                        fontWeight: (isSelected || isCorrect) ? 'bold' : 'normal',\r\n                        color: textColor\r\n                      }}\r\n                    >\r\n                      {icon && <span style={{ marginRight: '8px' }}>{icon}</span>}\r\n                      {optionLetter}. <HtmlContentRenderer htmlContent={option} />\r\n                      {label && <span style={{ marginLeft: '8px' }}>{label}</span>}\r\n                    </div>\r\n                  );\r\n                })}\r\n              </div>\r\n            );\r\n          }\r\n\r\n          // 降级处理：如果没有选项数据，显示字母\r\n          return userAnswer || '未选择';\r\n\r\n        case 'TRUE_FALSE':\r\n          return submittedAnswer.answer !== undefined ?\r\n            (submittedAnswer.answer ? '正确' : '错误') : '未选择';\r\n\r\n        case 'FILL_IN_BLANK':\r\n          if (submittedAnswer && typeof submittedAnswer === 'object') {\r\n            const answers = [];\r\n            Object.keys(submittedAnswer).sort().forEach(key => {\r\n              if (key.startsWith('blank_')) {\r\n                const answerValue = submittedAnswer[key] || '(空)';\r\n                // 使用renderAnswerContent渲染每个填空答案，支持数学公式\r\n                answers.push(renderAnswerContent(answerValue));\r\n              }\r\n            });\r\n            if (answers.length > 0) {\r\n              // 如果只有一个答案，直接返回\r\n              if (answers.length === 1) {\r\n                return answers[0];\r\n              }\r\n              // 多个答案时，用逗号分隔显示\r\n              return (\r\n                <div>\r\n                  {answers.map((answer, index) => (\r\n                    <span key={index}>\r\n                      {answer}\r\n                      {index < answers.length - 1 && ', '}\r\n                    </span>\r\n                  ))}\r\n                </div>\r\n              );\r\n            }\r\n            return '未填写';\r\n          }\r\n          return '未填写';\r\n\r\n        default:\r\n          return JSON.stringify(submittedAnswer);\r\n      }\r\n    } catch (error) {\r\n      return '答案解析失败';\r\n    }\r\n  };\r\n\r\n  // 渲染正确答案\r\n  const renderCorrectAnswer = (question) => {\r\n    try {\r\n      const questionDetails = question.questionDetails;\r\n      const questionType = questionDetails?.type || question.questionType;\r\n      const answer = questionDetails?.answer;\r\n\r\n      // 处理嵌套题型（阅读理解、听力题等）\r\n      if (questionType === 'READING_COMPREHENSION' || questionType === 'LISTENING' || questionType === 'CLOZE_TEST') {\r\n        console.log('renderCorrectAnswer - 处理嵌套题型:', questionType);\r\n        console.log('questionDetails:', questionDetails);\r\n\r\n        // 检查是否有子题目\r\n        if (questionDetails?.subQuestions && Array.isArray(questionDetails.subQuestions)) {\r\n          const subAnswers = [];\r\n          questionDetails.subQuestions.forEach((subQuestion, index) => {\r\n            const subAnswer = subQuestion.answer;\r\n            if (subAnswer) {\r\n              subAnswers.push(`第${index + 1}题：${subAnswer}`);\r\n            } else {\r\n              subAnswers.push(`第${index + 1}题：答案未设置`);\r\n            }\r\n          });\r\n          return subAnswers.join(', ');\r\n        }\r\n\r\n        // 如果没有子题目但有answer字段，尝试解析\r\n        if (answer) {\r\n          if (typeof answer === 'string') {\r\n            try {\r\n              const parsedAnswer = JSON.parse(answer);\r\n              if (typeof parsedAnswer === 'object' && parsedAnswer !== null) {\r\n                const answerEntries = Object.entries(parsedAnswer);\r\n                return answerEntries.map(([key, value]) => `${key}：${value}`).join(', ');\r\n              }\r\n            } catch (e) {\r\n              // 解析失败，直接返回字符串\r\n              return answer;\r\n            }\r\n          } else if (typeof answer === 'object' && answer !== null) {\r\n            const answerEntries = Object.entries(answer);\r\n            return answerEntries.map(([key, value]) => `${key}：${value}`).join(', ');\r\n          }\r\n        }\r\n\r\n        return '答案未设置';\r\n      }\r\n\r\n      switch (questionType) {\r\n        case 'SINGLE_CHOICE':\r\n        case 'MULTIPLE_CHOICE':\r\n          if (Array.isArray(answer)) {\r\n            return answer.join(', ');\r\n          }\r\n          return answer || '答案未设置';\r\n\r\n        case 'TRUE_FALSE':\r\n          return answer === true ? '正确' : answer === false ? '错误' : '答案未设置';\r\n\r\n        case 'FILL_IN_BLANK':\r\n          if (Array.isArray(answer)) {\r\n            return answer.join(', ');\r\n          }\r\n          return answer || '答案未设置';\r\n\r\n        default:\r\n          return answer ? String(answer) : '答案未设置';\r\n      }\r\n    } catch (error) {\r\n      console.error('renderCorrectAnswer 错误:', error);\r\n      return '答案解析失败';\r\n    }\r\n  };\r\n\r\n  // 渲染正确答案（支持HTML）\r\n  const renderCorrectAnswerWithHtml = (question) => {\r\n    try {\r\n      const questionDetails = question.questionDetails;\r\n      const questionType = questionDetails?.type || question.questionType;\r\n      const answer = questionDetails?.answer;\r\n\r\n      switch (questionType) {\r\n        case 'SINGLE_CHOICE':\r\n        case 'MULTIPLE_CHOICE':\r\n          // 获取题目选项\r\n          const options = questionDetails?.options || question.options;\r\n\r\n          if (options && Array.isArray(options) && answer) {\r\n            // 找到正确答案对应的选项内容\r\n            const answerIndex = answer.charCodeAt(0) - 65; // A=0, B=1, C=2, D=3\r\n            if (answerIndex >= 0 && answerIndex < options.length) {\r\n              const correctOptionContent = options[answerIndex];\r\n              return (\r\n                <div>\r\n                  <strong>{answer}. </strong>\r\n                  <HtmlContentRenderer htmlContent={correctOptionContent} />\r\n                </div>\r\n              );\r\n            }\r\n          }\r\n\r\n          // 降级处理\r\n          if (Array.isArray(answer)) {\r\n            const answerText = answer.join(', ');\r\n            if (answerText.includes('<')) {\r\n              return <HtmlContentRenderer htmlContent={answerText} />;\r\n            }\r\n            return answerText;\r\n          }\r\n          if (answer && typeof answer === 'string' && answer.includes('<')) {\r\n            return <HtmlContentRenderer htmlContent={answer} />;\r\n          }\r\n          return answer || '答案未设置';\r\n\r\n        case 'TRUE_FALSE':\r\n          return answer === true ? '正确' : answer === false ? '错误' : '答案未设置';\r\n\r\n        case 'FILL_IN_BLANK':\r\n          if (Array.isArray(answer)) {\r\n            const answerText = answer.join(', ');\r\n            // 如果答案包含HTML标签，使用HtmlContentRenderer\r\n            if (answerText.includes('<')) {\r\n              return <HtmlContentRenderer htmlContent={answerText} />;\r\n            }\r\n            return answerText;\r\n          }\r\n          // 如果答案包含HTML标签，使用HtmlContentRenderer\r\n          if (answer && typeof answer === 'string' && answer.includes('<')) {\r\n            return <HtmlContentRenderer htmlContent={answer} />;\r\n          }\r\n          return answer || '答案未设置';\r\n\r\n        default:\r\n          if (answer && typeof answer === 'string' && answer.includes('<')) {\r\n            return <HtmlContentRenderer htmlContent={answer} />;\r\n          }\r\n          return answer ? String(answer) : '答案未设置';\r\n      }\r\n    } catch (error) {\r\n      return '答案解析失败';\r\n    }\r\n  };\r\n\r\n  // 渲染填空题，将学生答案显示在下划线中\r\n  const renderFillInBlankWithAnswers = useCallback((questionContent, answerData, correctAnswers, questionResult = null) => {\r\n    try {\r\n      let content = questionContent;\r\n      let blankIndex = 0;\r\n\r\n      // 将下划线替换为学生答案\r\n      content = content.replace(/_+/g, () => {\r\n        const blankId = `blank_${blankIndex}`;\r\n        const userAnswer = answerData[blankId] || '';\r\n\r\n        // 处理正确答案 - 可能是数组格式\r\n        let correctAnswer = '';\r\n        if (Array.isArray(correctAnswers)) {\r\n          correctAnswer = correctAnswers[blankIndex] || '';\r\n        } else if (correctAnswers[blankId]) {\r\n          correctAnswer = correctAnswers[blankId];\r\n        }\r\n\r\n        // 🔧 使用后端返回的整体验证结果，而不是前端重复验证\r\n        // 如果有后端验证结果，使用后端结果；否则降级为简单比较\r\n        let isCorrect = false;\r\n        if (questionResult && questionResult.isCorrect !== undefined) {\r\n          // 使用后端的整体验证结果\r\n          isCorrect = questionResult.isCorrect;\r\n        } else {\r\n          // 降级处理：简单比较答案是否正确\r\n          isCorrect = userAnswer.trim().toLowerCase() === correctAnswer.trim().toLowerCase();\r\n        }\r\n        const statusIcon = isCorrect ? '✓' : '✗';\r\n        const statusColor = isCorrect ? '#52c41a' : '#ff4d4f';\r\n\r\n        blankIndex++;\r\n\r\n        // 如果有答案，显示答案和状态图标\r\n        if (userAnswer) {\r\n          return `<span style=\"display: inline-block; min-width: 60px; padding: 2px 8px; border-bottom: 2px solid ${statusColor}; background: transparent; text-align: center; position: relative;\">\r\n            ${userAnswer} <span style=\"color: ${statusColor}; font-weight: bold; margin-left: 4px;\">${statusIcon}</span>\r\n          </span>`;\r\n        } else {\r\n          // 没有答案，显示空白和错误图标\r\n          return `<span style=\"display: inline-block; min-width: 60px; padding: 2px 8px; border-bottom: 2px solid #ff4d4f; background: transparent; text-align: center; position: relative;\">\r\n            <span style=\"color: #999;\">(空)</span> <span style=\"color: #ff4d4f; font-weight: bold; margin-left: 4px;\">✗</span>\r\n          </span>`;\r\n        }\r\n      });\r\n\r\n      return <HtmlContentRenderer htmlContent={content} />;\r\n    } catch (error) {\r\n      console.error('渲染填空题答案失败:', error);\r\n      return '渲染失败';\r\n    }\r\n  }, []);\r\n\r\n  // 如果正在加载数据库数据，显示加载状态\r\n  if (loadingDbData) {\r\n    return (\r\n      <div className=\"scene-container result-screen\">\r\n        <div className=\"loading-spinner-container\">\r\n          <div style={{ fontSize: '18px', marginBottom: '20px', textAlign: 'center' }}>正在加载练习结果...</div>\r\n          <div className=\"spinner-lg\"></div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // 计算统计数据\r\n  const hasQuestions = validQuestions.length > 0;\r\n\r\n  // 简单的分数计算逻辑 - 基于已答题目的正确率\r\n  const calculateSimpleScore = (questions, userAnswers) => {\r\n    if (!questions.length) {\r\n      return { correctCount: 0, totalCount: 0, answeredCount: 0, accuracy: 0 };\r\n    }\r\n\r\n    let correctCount = 0;\r\n    let answeredCount = 0;\r\n\r\n    questions.forEach(question => {\r\n      const userAnswer = userAnswers[question.id];\r\n      if (userAnswer) {\r\n        answeredCount++;\r\n        if (userAnswer.isCorrect) {\r\n          correctCount++;\r\n        }\r\n      }\r\n    });\r\n\r\n    return {\r\n      correctCount,\r\n      totalCount: questions.length,\r\n      answeredCount,\r\n      // 基于已答题目的正确率：答对的题目数 / 已答题目数\r\n      accuracy: answeredCount > 0 ? correctCount / answeredCount : 0\r\n    };\r\n  };\r\n\r\n  const scoreResult = calculateSimpleScore(validQuestions, validUserAnswers);\r\n\r\n  // 兼容旧的stats格式\r\n  const stats = {\r\n    completionRate: scoreResult.totalCount > 0 ? (scoreResult.answeredCount / scoreResult.totalCount) * 100 : 0,\r\n    correctRate: scoreResult.accuracy || 0,\r\n    answeredQuestions: scoreResult.answeredCount || 0,\r\n    totalQuestions: scoreResult.totalCount || 0\r\n  };\r\n\r\n  // 计算掌握状态\r\n  const masteryLevel = stats.correctRate >= 0.8 ? '已掌握' : stats.correctRate >= 0.6 ? '基本掌握' : '未掌握';\r\n  const masteryIcon = stats.correctRate >= 0.8 ? '😊' : stats.correctRate >= 0.6 ? '😐' : '😞';\r\n  const masteryColor = stats.correctRate >= 0.8 ? '#52c41a' : stats.correctRate >= 0.6 ? '#faad14' : '#ff4d4f';\r\n\r\n  return (\r\n    <div className=\"scene-container result-screen\">\r\n      {/* 主要内容区域 - 使用bg1.png背景 */}\r\n      <div\r\n        className=\"result-main-content\"\r\n        style={{\r\n          backgroundImage: `url(${process.env.PUBLIC_URL}/images/bg1.png)`,\r\n          backgroundSize: 'cover',\r\n          backgroundPosition: 'center',\r\n          backgroundRepeat: 'no-repeat'\r\n        }}\r\n      >\r\n        {/* 结果卡片 - 优化布局 */}\r\n        <div className=\"result-card-optimized\">\r\n          {/* 头像和标题区域 */}\r\n          <div className=\"result-header-section\">\r\n            <div className=\"result-avatar\">\r\n              <span style={{ fontSize: '48px' }}>{masteryIcon}</span>\r\n            </div>\r\n            <Title level={3} className=\"result-title\">\r\n              {masteryLevel}\r\n            </Title>\r\n          </div>\r\n\r\n          {/* 知识点名称 */}\r\n          <div className=\"result-knowledge-section\">\r\n            <Title level={2} className=\"knowledge-point-name\">\r\n              {effectiveKnowledgePoint?.name || '知识点'}\r\n            </Title>\r\n          </div>\r\n\r\n          <div className=\"result-progress-bar\">\r\n            <div className=\"progress-container\">\r\n              <div\r\n                className=\"progress-fill\"\r\n                style={{\r\n                  width: `${Math.round(stats.correctRate * 100)}%`,\r\n                  backgroundColor: masteryColor\r\n                }}\r\n              ></div>\r\n            </div>\r\n            <span className=\"progress-text\">{Math.round(stats.correctRate * 100)}%</span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 历史查看提示 */}\r\n        {isHistoryView && (\r\n          <div style={{ textAlign: 'center', margin: '20px 0', color: '#666', fontSize: '14px' }}>\r\n            📚 这是您的历史练习记录\r\n          </div>\r\n        )}\r\n\r\n        {/* 知识点解析按钮 */}\r\n        <div style={{ textAlign: 'center', margin: '20px 0' }}>\r\n          <Button\r\n            className=\"knowledge-analysis-button\"\r\n            style={{\r\n              background: 'linear-gradient(135deg, #FF8A4C 0%, #F17A31 100%)',\r\n              border: '2px solid #D16428',\r\n              color: '#FFFFFF',\r\n              fontWeight: '600',\r\n              borderRadius: '24px',\r\n              padding: '8px 24px',\r\n              height: '40px',\r\n              fontSize: '14px',\r\n              boxShadow: '0 4px 12px rgba(241, 122, 49, 0.3)',\r\n              transition: 'all 0.2s ease'\r\n            }}\r\n            onMouseEnter={(e) => {\r\n              e.target.style.background = 'linear-gradient(135deg, #FF9A5C 0%, #E16A28 100%)';\r\n              e.target.style.transform = 'translateY(-2px)';\r\n              e.target.style.boxShadow = '0 6px 16px rgba(241, 122, 49, 0.4)';\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              e.target.style.background = 'linear-gradient(135deg, #FF8A4C 0%, #F17A31 100%)';\r\n              e.target.style.transform = 'translateY(0)';\r\n              e.target.style.boxShadow = '0 4px 12px rgba(241, 122, 49, 0.3)';\r\n            }}\r\n            onClick={handleKnowledgeAnalysis}\r\n          >\r\n            知识点解析\r\n          </Button>\r\n        </div>\r\n\r\n        {/* 操作区域 - 紧贴结果卡片 */}\r\n        <div className=\"result-actions-area\">\r\n          {/* 错题统计 */}\r\n          {hasQuestions && (\r\n            <div className=\"wrong-questions-summary\">\r\n              <span className=\"summary-label\">查看题目：</span>\r\n              {validQuestions.map((_, index) => {\r\n                const questionAnswer = validUserAnswers[validQuestions[index]?.id];\r\n                const isCorrect = questionAnswer?.isCorrect;\r\n                const isSkipped = questionAnswer?.isSkipped;\r\n                return (\r\n                  <span\r\n                    key={index}\r\n                    className={`result-question-number ${\r\n                      isSkipped ? 'skipped' : (isCorrect ? 'correct' : 'wrong')\r\n                    }`}\r\n                    onClick={() => handleQuestionClick(index)}\r\n                    style={{ cursor: 'pointer' }}\r\n                    title=\"点击查看题目详情\"\r\n                  >\r\n                    {index + 1}\r\n                  </span>\r\n                );\r\n              })}\r\n            </div>\r\n          )}\r\n\r\n          {/* 统一的按钮组 */}\r\n          <div className=\"result-button-group\">\r\n            {onRestart && (\r\n              <Button\r\n                className=\"restart-button\"\r\n                onClick={onRestart}\r\n                style={{\r\n                  background: '#FFFFFF',\r\n                  border: '2px solid #FF8A4C',\r\n                  color: '#FF8A4C',\r\n                  fontWeight: '600',\r\n                  borderRadius: '24px',\r\n                  padding: '8px 24px',\r\n                  height: '40px',\r\n                  fontSize: '14px',\r\n                  width: '120px',\r\n                  transition: 'all 0.2s ease'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.target.style.background = '#FFF5F0';\r\n                  e.target.style.transform = 'translateY(-2px)';\r\n                  e.target.style.boxShadow = '0 4px 12px rgba(255, 138, 76, 0.3)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.target.style.background = '#FFFFFF';\r\n                  e.target.style.transform = 'translateY(0)';\r\n                  e.target.style.boxShadow = 'none';\r\n                }}\r\n              >\r\n                再来一次\r\n              </Button>\r\n            )}\r\n            <Button\r\n              type=\"primary\"\r\n              size=\"large\"\r\n              className=\"primary-button\"\r\n              onClick={() => window.history.back()}\r\n            >\r\n              返回\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 统一的题目详情弹窗 */}\r\n      <QuestionDetailModal\r\n        visible={modalVisible}\r\n        onClose={handleModalClose}\r\n        selectedQuestion={selectedQuestion}\r\n        renderStudentAnswer={renderStudentAnswer}\r\n        renderCorrectAnswer={renderCorrectAnswerWithHtml}\r\n        renderFillInBlankWithAnswers={renderFillInBlankWithAnswers}\r\n      />\r\n\r\n      {/* 知识点解析弹窗 */}\r\n      <Modal\r\n        title={null}\r\n        open={knowledgeAnalysisVisible}\r\n        onCancel={handleKnowledgeAnalysisClose}\r\n        footer={null}\r\n        width={1200}\r\n        centered\r\n        closable={false}\r\n        className=\"knowledge-analysis-modal\"\r\n        styles={{\r\n          body: { padding: 0 }\r\n        }}\r\n      >\r\n        {/* 自定义标签块 */}\r\n        <div className=\"task-kpoint__exp__tabs\" style={{\r\n          background: 'rgba(0, 0, 0, 0.8)',\r\n          padding: '0',\r\n          position: 'relative',\r\n          width: '100%'\r\n        }}>\r\n          <div style={{\r\n            width: '100%',\r\n            padding: '12px 20px',\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            justifyContent: 'flex-start',\r\n            position: 'relative'\r\n          }}>\r\n            <div className=\"task-kpoint__exp__tab task-kpoint__exp__tab_active\" style={{\r\n              background: '#ff6b35',\r\n              color: '#ffffff',\r\n              padding: '8px 16px',\r\n              borderRadius: '6px 6px 0 0',\r\n              fontSize: '14px',\r\n              fontWeight: '600',\r\n              marginRight: 'auto'\r\n            }}>\r\n              知识点视频\r\n            </div>\r\n            <div className=\"task-kpoint__exp__close\" onClick={handleKnowledgeAnalysisClose} style={{\r\n              cursor: 'pointer',\r\n              color: '#ffffff',\r\n              fontSize: '16px',\r\n              padding: '4px',\r\n              position: 'absolute',\r\n              right: '20px',\r\n              top: '50%',\r\n              transform: 'translateY(-50%)'\r\n            }}>\r\n              <CloseOutlined />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"video-analysis-container\" style={{\r\n          display: 'flex',\r\n          height: '600px',\r\n          background: '#ffffff'\r\n        }}>\r\n          {/* 左侧视频播放区域 - 80%宽度 */}\r\n          <div style={{\r\n            width: '80%',\r\n            display: 'flex',\r\n            flexDirection: 'column',\r\n            background: '#ffffff',\r\n            position: 'relative'\r\n          }}>\r\n            <div style={{\r\n              flex: 1,\r\n              position: 'relative',\r\n              minHeight: '500px',\r\n              display: 'flex',\r\n              alignItems: 'center',\r\n              justifyContent: 'center'\r\n            }}>\r\n              {loadingVideos ? (\r\n                <div style={{\r\n                  color: '#ffffff',\r\n                  fontSize: '16px',\r\n                  textAlign: 'center'\r\n                }}>\r\n                  <PlayCircleOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />\r\n                  <div>正在加载视频...</div>\r\n                </div>\r\n              ) : currentVideo ? (\r\n                <ReactPlayer\r\n                  url={currentVideo.videoUrl}\r\n                  width=\"100%\"\r\n                  height=\"100%\"\r\n                  playing={playing}\r\n                  playbackRate={playbackRate}\r\n                  controls={true}\r\n                  light={currentVideo.coverImageUrl}\r\n                  onPlay={() => setPlaying(true)}\r\n                  onPause={() => setPlaying(false)}\r\n                  onError={(error) => {\r\n                    console.error('视频播放错误:', error);\r\n                    message.error('视频播放失败');\r\n                  }}\r\n                  config={{\r\n                    file: {\r\n                      attributes: {\r\n                        style: { width: '100%', height: '100%' },\r\n                        controlsList: 'nodownload',\r\n                        disablePictureInPicture: false\r\n                      }\r\n                    }\r\n                  }}\r\n                />\r\n              ) : (\r\n                <div style={{\r\n                  color: '#ffffff',\r\n                  fontSize: '16px',\r\n                  textAlign: 'center'\r\n                }}>\r\n                  <PlayCircleOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />\r\n                  <div>暂无视频</div>\r\n                </div>\r\n              )}\r\n\r\n              {/* 播放速度控制 */}\r\n              {currentVideo && (\r\n                <div style={{\r\n                  position: 'absolute',\r\n                  bottom: '10px',\r\n                  right: '10px',\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  gap: '8px',\r\n                  background: 'rgba(0, 0, 0, 0.7)',\r\n                  padding: '6px 12px',\r\n                  borderRadius: '20px',\r\n                  color: '#ffffff',\r\n                  fontSize: '12px'\r\n                }}>\r\n                  <span>速度:</span>\r\n                  <select\r\n                    value={playbackRate}\r\n                    onChange={(e) => setPlaybackRate(parseFloat(e.target.value))}\r\n                    style={{\r\n                      background: 'transparent',\r\n                      border: 'none',\r\n                      color: '#ffffff',\r\n                      fontSize: '12px',\r\n                      cursor: 'pointer',\r\n                      outline: 'none'\r\n                    }}\r\n                  >\r\n                    <option value={0.5} style={{ background: '#333', color: '#fff' }}>0.5x</option>\r\n                    <option value={0.75} style={{ background: '#333', color: '#fff' }}>0.75x</option>\r\n                    <option value={1} style={{ background: '#333', color: '#fff' }}>1x</option>\r\n                    <option value={1.25} style={{ background: '#333', color: '#fff' }}>1.25x</option>\r\n                    <option value={1.5} style={{ background: '#333', color: '#fff' }}>1.5x</option>\r\n                    <option value={2} style={{ background: '#333', color: '#fff' }}>2x</option>\r\n                  </select>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* 右侧视频列表区域 - 20%宽度 */}\r\n          <div style={{\r\n            width: '20%',\r\n            background: '#2f2f2f',\r\n            padding: '20px',\r\n            display: 'flex',\r\n            flexDirection: 'column',\r\n            gap: '16px'\r\n          }}>\r\n            <div>\r\n              <h4 style={{\r\n                margin: '0 0 16px 0',\r\n                color: '#ffffff',\r\n                fontSize: '16px',\r\n                fontWeight: '600'\r\n              }}>\r\n                相关知识点视频\r\n              </h4>\r\n            </div>\r\n\r\n            {/* 视频列表 */}\r\n            <div className=\"video-list-container\" style={{ flex: 1, overflowY: 'auto' }}>\r\n              {videoList.map((video, index) => (\r\n                <div\r\n                  key={video.id}\r\n                  onClick={() => handleVideoSelect(index)}\r\n                  className={`video-list-item ${currentVideoIndex === index ? 'active' : ''}`}\r\n                  style={{\r\n                    padding: '12px',\r\n                    margin: '0 0 8px 0',\r\n                    background: currentVideoIndex === index ? '#404040' : 'transparent',\r\n                    borderRadius: '6px',\r\n                    cursor: 'pointer',\r\n                    transition: 'all 0.2s',\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    gap: '12px'\r\n                  }}\r\n                >\r\n                  {/* 橙色序号标签 */}\r\n                  <div style={{\r\n                    background: '#ff6b35',\r\n                    color: '#ffffff',\r\n                    fontSize: '12px',\r\n                    fontWeight: '600',\r\n                    padding: '4px 8px',\r\n                    borderRadius: '4px',\r\n                    minWidth: '24px',\r\n                    textAlign: 'center'\r\n                  }}>\r\n                    {index + 1}\r\n                  </div>\r\n\r\n                  {/* 视频信息 */}\r\n                  <div style={{ flex: 1, minWidth: 0 }}>\r\n                    <div style={{\r\n                      fontWeight: '500',\r\n                      color: '#ffffff',\r\n                      fontSize: '13px',\r\n                      marginBottom: '6px',\r\n                      overflow: 'hidden',\r\n                      textOverflow: 'ellipsis',\r\n                      whiteSpace: 'nowrap'\r\n                    }}>\r\n                      {video.title.replace(/_\\d{14}$/, '')}\r\n                    </div>\r\n                    <div style={{\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      gap: '12px',\r\n                      fontSize: '11px',\r\n                      color: '#cccccc'\r\n                    }}>\r\n                      {video.durationSeconds && (\r\n                        <span>\r\n                          {Math.floor(video.durationSeconds / 60).toString().padStart(2, '0')}:{(video.durationSeconds % 60).toString().padStart(2, '0')}\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n\r\n\r\n\r\n\r\n\r\n\r\n          </div>\r\n        </div>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nResultScreen.propTypes = {\r\n  knowledgePoint: PropTypes.shape({\r\n    id: PropTypes.number,\r\n    name: PropTypes.string,\r\n    chapterName: PropTypes.string,\r\n    subjectName: PropTypes.string\r\n  }),\r\n  progress: PropTypes.number,\r\n  userAnswers: PropTypes.object,\r\n  questions: PropTypes.array,\r\n  onRestart: PropTypes.func,\r\n  onViewQuestions: PropTypes.func,\r\n  sessionId: PropTypes.number, // 可选的练习会话ID\r\n  testStartTime: PropTypes.number, // 测试开始时间戳\r\n  testEndTime: PropTypes.number // 测试结束时间戳\r\n};\r\n\r\nexport default ResultScreen; "], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAChE,SAASC,UAAU,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAEC,OAAO,QAAQ,MAAM;AAC9D,SAASC,mBAAmB,EAAEC,aAAa,EAAEC,mBAAmB,EAAEC,yBAAyB,EAAEC,kBAAkB,EAAEC,aAAa,QAAQ,mBAAmB;AACzJ,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,iCAAiC;AACxC,OAAO,oBAAoB;AAC3B,OAAOC,mBAAmB,MAAM,mCAAmC;AACnE,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,OAAOC,mBAAmB,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAM;EAAEC;AAAM,CAAC,GAAGpB,UAAU;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqB,YAAY,GAAGA,CAAC;EAAEC,cAAc;EAAEC,QAAQ;EAAEC,WAAW,GAAG,CAAC,CAAC;EAAEC,SAAS,GAAG,EAAE;EAAEC,SAAS;EAAEC,eAAe;EAAEC,SAAS;EAAEC,aAAa,GAAG,KAAK;EAAEC,aAAa;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EACjLC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;IAC1CZ,cAAc,EAAEA,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEa,IAAI;IACpCC,cAAc,EAAEX,SAAS,CAACY,MAAM;IAChCC,gBAAgB,EAAEC,MAAM,CAACC,IAAI,CAAChB,WAAW,CAAC,CAACa,MAAM;IACjDT,SAAS,EAAEA;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACa,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC+C,YAAY,EAAEC,eAAe,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACmD,MAAM,EAAEC,SAAS,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACqD,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxD,QAAQ,CAACgC,SAAS,CAAC;;EAEnE;EACA,MAAM,CAACyB,wBAAwB,EAAEC,2BAA2B,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAAC2D,YAAY,EAAEC,eAAe,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC6D,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACiE,SAAS,EAAEC,YAAY,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpE,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACqE,aAAa,EAAEC,gBAAgB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAEzDqC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;IAClCiB,gBAAgB;IAChBgB,SAAS,EAAE,CAAC,CAACpB,MAAM;IACnBE;EACF,CAAC,CAAC;;EAEF;EACA,MAAMmB,cAAc,GAAGtE,MAAM,CAAC,KAAK,CAAC;;EAEpC;EACAD,SAAS,CAAC,MAAM;IACdoC,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEiB,gBAAgB,EAAE,SAAS,EAAE,CAAC,CAACJ,MAAM,EAAE,gBAAgB,EAAEE,aAAa,CAAC;IAEpI,MAAMoB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC;MACA,IAAIlB,gBAAgB,IAAI,CAACJ,MAAM,IAAI,CAACE,aAAa,EAAE;QACjDhB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEiB,gBAAgB,CAAC;QACxED,gBAAgB,CAAC,IAAI,CAAC;QAEtB,IAAI;UACFjB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrD,MAAMoC,cAAc,GAAG,MAAMvD,WAAW,CAACwD,iBAAiB,CAACpB,gBAAgB,CAAC;UAC5ElB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEoC,cAAc,CAAC;UAEtE,MAAME,aAAa,GAAG,MAAMzD,WAAW,CAAC0D,4BAA4B,CAACH,cAAc,CAAC;UACpFrC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEsC,aAAa,CAAC;UAEpD,IAAIA,aAAa,EAAE;YACjBvC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAChDc,SAAS,CAACwB,aAAa,CAAC;UAC1B,CAAC,MAAM;YACLvC,OAAO,CAACyC,IAAI,CAAC,0BAA0B,CAAC;UAC1C;QACF,CAAC,CAAC,OAAOC,KAAK,EAAE;UACd1C,OAAO,CAAC0C,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACnD,CAAC,SAAS;UACR1C,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;UAC1DgB,gBAAgB,CAAC,KAAK,CAAC;QACzB;MACF,CAAC,MAAM;QACLjB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;UACvC0C,YAAY,EAAE,CAAC,CAACzB,gBAAgB;UAChCgB,SAAS,EAAE,CAAC,CAACpB,MAAM;UACnB8B,SAAS,EAAE5B;QACb,CAAC,CAAC;MACJ;IACF,CAAC;IAEDoB,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAAClB,gBAAgB,EAAEJ,MAAM,EAAEE,aAAa,CAAC,CAAC;;EAE7C;EACA,MAAM6B,uBAAuB,GAAG,CAAA/B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEzB,cAAc,KAAIA,cAAc;EACxE,MAAMyD,kBAAkB,GAAG,CAAAhC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEtB,SAAS,KAAIA,SAAS;EACzD,MAAMuD,oBAAoB,GAAG,CAAAjC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEvB,WAAW,KAAIA,WAAW;;EAE/D;EACA,MAAMyD,cAAc,GAAGC,KAAK,CAACC,OAAO,CAACJ,kBAAkB,CAAC,GAAGA,kBAAkB,GAAG,EAAE;EAClF,MAAMK,gBAAgB,GAAGJ,oBAAoB,IAAI,OAAOA,oBAAoB,KAAK,QAAQ,GAAGA,oBAAoB,GAAG,CAAC,CAAC;;EAErH;EACAnF,SAAS,CAAC,MAAM;IACd,MAAMwF,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC;MACA,IAAIxC,mBAAmB,IAAIuB,cAAc,CAACkB,OAAO,IAAI,EAAChE,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAEiE,EAAE,KAAIN,cAAc,CAAC5C,MAAM,KAAK,CAAC,EAAE;QACvGJ,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;UAClCW,mBAAmB;UACnBuB,cAAc,EAAEA,cAAc,CAACkB,OAAO;UACtCE,iBAAiB,EAAE,CAAC,EAAClE,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAEiE,EAAE;UACvCnD,cAAc,EAAE6C,cAAc,CAAC5C;QACjC,CAAC,CAAC;QACF;MACF;MAEAJ,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACtCkC,cAAc,CAACkB,OAAO,GAAG,IAAI;MAE7B,IAAI;QACF;QACA,MAAMG,qBAAqB,GAAG3D,aAAa,IAAIC,WAAW,GACxD2D,IAAI,CAACC,KAAK,CAAC,CAAC5D,WAAW,GAAGD,aAAa,IAAI,IAAI,CAAC,GAChDmD,cAAc,CAAC5C,MAAM,GAAG,EAAE,CAAC,CAAC;;QAE9B;QACA,MAAMuD,YAAY,GAAG;UACnBC,gBAAgB,EAAEvE,cAAc,CAACiE,EAAE;UACnCO,WAAW,EAAE,0BAA0B;UACvCC,eAAe,EAAEN,qBAAqB;UAAE;UACxChE,SAAS,EAAEwD,cAAc,CAACe,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;YAAA,IAAAC,qBAAA;YACjD,MAAMC,cAAc,GAAGhB,gBAAgB,CAACa,QAAQ,CAACV,EAAE,CAAC;;YAEpD;YACA,IAAIc,YAAY,GAAG,cAAc;YACjC,IAAID,cAAc,EAAE;cAClB,IAAIA,cAAc,CAACE,SAAS,EAAE;gBAC5BD,YAAY,GAAG,SAAS;cAC1B,CAAC,MAAM,IAAID,cAAc,CAACG,SAAS,EAAE;gBACnCF,YAAY,GAAG,SAAS;cAC1B,CAAC,MAAM;gBACLA,YAAY,GAAG,WAAW;cAC5B;YACF;;YAEA;YACA,IAAIG,aAAa,GAAG,IAAI;YACxB,IAAIJ,cAAc,IAAI,CAACA,cAAc,CAACE,SAAS,EAAE;cAC/C;cACA,MAAM;gBAAEC,SAAS;gBAAED,SAAS;gBAAEG,QAAQ;gBAAE,GAAGC;cAAe,CAAC,GAAGN,cAAc;cAC5E,IAAI7D,MAAM,CAACC,IAAI,CAACkE,cAAc,CAAC,CAACrE,MAAM,GAAG,CAAC,EAAE;gBAC1CmE,aAAa,GAAGG,IAAI,CAACC,SAAS,CAACF,cAAc,CAAC;cAChD;YACF;YAEA,OAAO;cACLG,UAAU,EAAEZ,QAAQ,CAACV,EAAE;cACvBuB,aAAa,EAAEZ,KAAK,GAAG,CAAC;cACxBa,gBAAgB,EAAEJ,IAAI,CAACC,SAAS,CAACX,QAAQ,CAAC;cAC1CO,aAAa,EAAEA,aAAa;cAC5BQ,aAAa,EAAEL,IAAI,CAACC,SAAS,CAAC,EAAAT,qBAAA,GAAAF,QAAQ,CAACgB,eAAe,cAAAd,qBAAA,uBAAxBA,qBAAA,CAA0Be,MAAM,KAAIjB,QAAQ,CAACiB,MAAM,IAAI,EAAE,CAAC;cACxFb,YAAY,EAAEA;YAChB,CAAC;UACH,CAAC;QACH,CAAC;QAEDpE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE0D,YAAY,CAAC;QAEtC,MAAMuB,QAAQ,GAAG,MAAMpG,WAAW,CAACsE,kBAAkB,CAACO,YAAY,CAAC;QACnE3D,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEiF,QAAQ,CAAC;QAElCrE,sBAAsB,CAAC,IAAI,CAAC;QAC5B1C,OAAO,CAACgH,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;;QAE7B;QACA,IAAID,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEvF,SAAS,IAAI,CAACuB,gBAAgB,EAAE;UAC5ClB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEiF,QAAQ,CAACvF,SAAS,CAAC;UACrEwB,mBAAmB,CAAC+D,QAAQ,CAACvF,SAAS,CAAC;QACzC;MAEF,CAAC,CAAC,OAAO+C,KAAK,EAAE;QACd1C,OAAO,CAAC0C,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC;MACF,CAAC,SAAS;QACRP,cAAc,CAACkB,OAAO,GAAG,KAAK;MAChC;IACF,CAAC;IAEDD,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAAC/D,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEiE,EAAE,EAAEN,cAAc,EAAEG,gBAAgB,EAAEvC,mBAAmB,CAAC,CAAC;;EAE/E;EACA,MAAMwE,mBAAmB,GAAIC,aAAa,IAAK;IAC7C,MAAMrB,QAAQ,GAAGhB,cAAc,CAACqC,aAAa,CAAC;IAC9C,MAAMlB,cAAc,GAAGhB,gBAAgB,CAACa,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEV,EAAE,CAAC;IAErD,IAAIU,QAAQ,EAAE;MACZvD,mBAAmB,CAAC;QAClBuD,QAAQ;QACRG,cAAc;QACdF,KAAK,EAAEoB,aAAa,GAAG;MACzB,CAAC,CAAC;MACF1E,eAAe,CAAC,IAAI,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM2E,gBAAgB,GAAGA,CAAA,KAAM;IAC7B3E,eAAe,CAAC,KAAK,CAAC;IACtBF,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM8E,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1CvF,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEZ,cAAc,CAAC;IAE9C,IAAI,EAACA,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAEmG,iBAAiB,GAAE;MACtCrH,OAAO,CAACsH,OAAO,CAAC,YAAY,CAAC;MAC7B;IACF;IAEAxD,gBAAgB,CAAC,IAAI,CAAC;IAEtB,IAAI;MACF;MACA,MAAMiD,QAAQ,GAAG,MAAMpG,WAAW,CAAC4G,qBAAqB,CAACrG,cAAc,CAACmG,iBAAiB,CAAC;MAC1FxF,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEiF,QAAQ,CAAC;;MAElC;MACA,MAAMS,SAAS,GAAGT,QAAQ,CAACU,IAAI,IAAIV,QAAQ;MAC3C,IAAIS,SAAS,IAAIA,SAAS,CAACvF,MAAM,GAAG,CAAC,EAAE;QACrC;QACA,MAAMyF,MAAM,GAAGF,SAAS,CAAC5B,GAAG,CAAC+B,KAAK,KAAK;UACrCxC,EAAE,EAAEwC,KAAK,CAACxC,EAAE;UACZyC,KAAK,EAAED,KAAK,CAACC,KAAK,IAAI,GAAG1G,cAAc,CAACa,IAAI,MAAM;UAClD8F,QAAQ,EAAEF,KAAK,CAACE,QAAQ;UACxBC,aAAa,EAAEH,KAAK,CAACG,aAAa;UAClCC,WAAW,EAAEJ,KAAK,CAACI,WAAW;UAC9BpC,eAAe,EAAEgC,KAAK,CAAChC;QACzB,CAAC,CAAC,CAAC;QAEHjC,YAAY,CAACgE,MAAM,CAAC;QACpBtE,eAAe,CAACsE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B9D,oBAAoB,CAAC,CAAC,CAAC;QACvBV,2BAA2B,CAAC,IAAI,CAAC;MACnC,CAAC,MAAM;QACLlD,OAAO,CAACsH,OAAO,CAAC,eAAe,CAAC;MAClC;IACF,CAAC,CAAC,OAAO/C,KAAK,EAAE;MACd1C,OAAO,CAAC0C,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClCvE,OAAO,CAACuE,KAAK,CAAC,gBAAgB,CAAC;IACjC,CAAC,SAAS;MACRT,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMkE,4BAA4B,GAAGA,CAAA,KAAM;IACzC9E,2BAA2B,CAAC,KAAK,CAAC;IAClCI,UAAU,CAAC,KAAK,CAAC;IACjBF,eAAe,CAAC,IAAI,CAAC;IACrBM,YAAY,CAAC,EAAE,CAAC;IAChBE,oBAAoB,CAAC,CAAC,CAAC;IACvBE,gBAAgB,CAAC,KAAK,CAAC;EACzB,CAAC;;EAED;EACA,MAAMmE,iBAAiB,GAAInC,KAAK,IAAK;IACnC,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGrC,SAAS,CAACxB,MAAM,EAAE;MAC1C2B,oBAAoB,CAACkC,KAAK,CAAC;MAC3B1C,eAAe,CAACK,SAAS,CAACqC,KAAK,CAAC,CAAC;MACjCxC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM4E,mBAAmB,GAAIC,OAAO,IAAK;IACvC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;IAEvB,MAAMC,UAAU,GAAGC,MAAM,CAACF,OAAO,CAAC;;IAElC;IACA,IAAIC,UAAU,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC5B,oBACEvH,OAAA,CAACN,mBAAmB;QAClB8H,WAAW,EAAEH,UAAW;QACxBI,OAAO,EAAGjE,KAAK,IAAK1C,OAAO,CAAC0C,KAAK,CAAC,WAAW,EAAEA,KAAK;MAAE;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC;IAEN;;IAEA;IACA,IAAIR,UAAU,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;MACjH,oBACEvH,OAAA;QAAK8H,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO,CAAE;QAAAC,QAAA,eAChChI,OAAA,CAACH,OAAO;UAAAmI,QAAA,EACL,IAAIX,UAAU;QAAG;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAEV;;IAEA;IACA,OAAOR,UAAU;EACnB,CAAC;;EAED;EACA,MAAMY,mBAAmB,GAAGA,CAAChD,cAAc,EAAEH,QAAQ,KAAK;IAAA,IAAAoD,sBAAA,EAAAC,sBAAA;IACxD,IAAI;MAAA,IAAAC,sBAAA;MACF;MACA,IAAIC,eAAe,GAAG,CAAC,CAAC;MACxB,IAAIpD,cAAc,CAACoD,eAAe,EAAE;QAClCA,eAAe,GAAG7C,IAAI,CAAC8C,KAAK,CAACrD,cAAc,CAACoD,eAAe,CAAC;MAC9D,CAAC,MAAM;QACL;QACA,MAAM;UAAEjD,SAAS;UAAED,SAAS;UAAEG,QAAQ;UAAE,GAAGiD;QAAW,CAAC,GAAGtD,cAAc;QACxEoD,eAAe,GAAGE,UAAU;MAC9B;MAEA,MAAMC,YAAY,GAAG,EAAAJ,sBAAA,GAAAtD,QAAQ,CAACgB,eAAe,cAAAsC,sBAAA,uBAAxBA,sBAAA,CAA0BK,IAAI,KAAI3D,QAAQ,CAAC0D,YAAY;MAE5E,QAAQA,YAAY;QAClB,KAAK,eAAe;QACpB,KAAK,iBAAiB;UACpB;UACA,IAAIE,UAAU,GAAG,EAAE;UACnB,IAAIL,eAAe,CAACM,eAAe,EAAE;YACnCD,UAAU,GAAGL,eAAe,CAACM,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,MAAM,IAAIvH,MAAM,CAACC,IAAI,CAACgH,eAAe,CAAC,CAACnH,MAAM,GAAG,CAAC,EAAE;YAClD,MAAM0H,MAAM,GAAGxH,MAAM,CAACwH,MAAM,CAACP,eAAe,CAAC,CAACQ,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAKC,SAAS,CAAC;YACxFL,UAAU,GAAGE,MAAM,CAAC1H,MAAM,GAAG,CAAC,GAAG0H,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE;UACjD;;UAEA;UACA,MAAMI,OAAO,GAAG,EAAAd,sBAAA,GAAApD,QAAQ,CAACgB,eAAe,cAAAoC,sBAAA,uBAAxBA,sBAAA,CAA0Bc,OAAO,KAAIlE,QAAQ,CAACkE,OAAO;UACrE,MAAMnD,aAAa,GAAG,EAAAsC,sBAAA,GAAArD,QAAQ,CAACgB,eAAe,cAAAqC,sBAAA,uBAAxBA,sBAAA,CAA0BpC,MAAM,KAAIjB,QAAQ,CAACiB,MAAM;UAEzE,IAAIiD,OAAO,IAAIjF,KAAK,CAACC,OAAO,CAACgF,OAAO,CAAC,EAAE;YACrC,oBACEhJ,OAAA;cAAAgI,QAAA,EACGgB,OAAO,CAACnE,GAAG,CAAC,CAACoE,MAAM,EAAEC,WAAW,KAAK;gBACpC,MAAMC,YAAY,GAAG7B,MAAM,CAAC8B,YAAY,CAAC,EAAE,GAAGF,WAAW,CAAC,CAAC,CAAC;gBAC5D,MAAMG,UAAU,GAAGX,UAAU,KAAKS,YAAY;gBAC9C,MAAM/D,SAAS,GAAGS,aAAa,KAAKsD,YAAY;;gBAEhD;gBACA,IAAIG,eAAe,GAAG,aAAa;gBACnC,IAAIC,WAAW,GAAG,SAAS;gBAC3B,IAAIC,SAAS,GAAG,MAAM;gBACtB,IAAIC,IAAI,GAAG,EAAE;gBACb,IAAIC,KAAK,GAAG,EAAE;gBAEd,IAAIL,UAAU,IAAIjE,SAAS,EAAE;kBAC3B;kBACAkE,eAAe,GAAG,SAAS;kBAC3BC,WAAW,GAAG,SAAS;kBACvBC,SAAS,GAAG,SAAS;kBACrBC,IAAI,GAAG,GAAG;kBACVC,KAAK,GAAG,aAAa;gBACvB,CAAC,MAAM,IAAIL,UAAU,IAAI,CAACjE,SAAS,EAAE;kBACnC;kBACAkE,eAAe,GAAG,SAAS;kBAC3BC,WAAW,GAAG,SAAS;kBACvBC,SAAS,GAAG,SAAS;kBACrBC,IAAI,GAAG,GAAG;kBACVC,KAAK,GAAG,aAAa;gBACvB,CAAC,MAAM,IAAI,CAACL,UAAU,IAAIjE,SAAS,EAAE;kBACnC;kBACAkE,eAAe,GAAG,SAAS;kBAC3BC,WAAW,GAAG,SAAS;kBACvBC,SAAS,GAAG,SAAS;kBACrBC,IAAI,GAAG,GAAG;kBACVC,KAAK,GAAG,QAAQ;gBAClB;gBAEA,oBACE1J,OAAA;kBAEE8H,KAAK,EAAE;oBACL6B,YAAY,EAAE,KAAK;oBACnBC,OAAO,EAAE,UAAU;oBACnBC,YAAY,EAAE,KAAK;oBACnBP,eAAe;oBACfQ,MAAM,EAAE,aAAaP,WAAW,EAAE;oBAClCQ,UAAU,EAAGV,UAAU,IAAIjE,SAAS,GAAI,MAAM,GAAG,QAAQ;oBACzD4E,KAAK,EAAER;kBACT,CAAE;kBAAAxB,QAAA,GAEDyB,IAAI,iBAAIzJ,OAAA;oBAAM8H,KAAK,EAAE;sBAAEmC,WAAW,EAAE;oBAAM,CAAE;oBAAAjC,QAAA,EAAEyB;kBAAI;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC1DsB,YAAY,EAAC,IAAE,eAAAnJ,OAAA,CAACN,mBAAmB;oBAAC8H,WAAW,EAAEyB;kBAAO;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAC3D6B,KAAK,iBAAI1J,OAAA;oBAAM8H,KAAK,EAAE;sBAAEoC,UAAU,EAAE;oBAAM,CAAE;oBAAAlC,QAAA,EAAE0B;kBAAK;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAbvDqB,WAAW;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcb,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAEV;;UAEA;UACA,OAAOa,UAAU,IAAI,KAAK;QAE5B,KAAK,YAAY;UACf,OAAOL,eAAe,CAACtC,MAAM,KAAKgD,SAAS,GACxCV,eAAe,CAACtC,MAAM,GAAG,IAAI,GAAG,IAAI,GAAI,KAAK;QAElD,KAAK,eAAe;UAClB,IAAIsC,eAAe,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;YAC1D,MAAM8B,OAAO,GAAG,EAAE;YAClB/I,MAAM,CAACC,IAAI,CAACgH,eAAe,CAAC,CAAC+B,IAAI,CAAC,CAAC,CAACC,OAAO,CAACC,GAAG,IAAI;cACjD,IAAIA,GAAG,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;gBAC5B,MAAMC,WAAW,GAAGnC,eAAe,CAACiC,GAAG,CAAC,IAAI,KAAK;gBACjD;gBACAH,OAAO,CAACM,IAAI,CAACtD,mBAAmB,CAACqD,WAAW,CAAC,CAAC;cAChD;YACF,CAAC,CAAC;YACF,IAAIL,OAAO,CAACjJ,MAAM,GAAG,CAAC,EAAE;cACtB;cACA,IAAIiJ,OAAO,CAACjJ,MAAM,KAAK,CAAC,EAAE;gBACxB,OAAOiJ,OAAO,CAAC,CAAC,CAAC;cACnB;cACA;cACA,oBACEnK,OAAA;gBAAAgI,QAAA,EACGmC,OAAO,CAACtF,GAAG,CAAC,CAACkB,MAAM,EAAEhB,KAAK,kBACzB/E,OAAA;kBAAAgI,QAAA,GACGjC,MAAM,EACNhB,KAAK,GAAGoF,OAAO,CAACjJ,MAAM,GAAG,CAAC,IAAI,IAAI;gBAAA,GAF1B6D,KAAK;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAEV;YACA,OAAO,KAAK;UACd;UACA,OAAO,KAAK;QAEd;UACE,OAAOrC,IAAI,CAACC,SAAS,CAAC4C,eAAe,CAAC;MAC1C;IACF,CAAC,CAAC,OAAO7E,KAAK,EAAE;MACd,OAAO,QAAQ;IACjB;EACF,CAAC;;EAED;EACA,MAAMkH,mBAAmB,GAAI5F,QAAQ,IAAK;IACxC,IAAI;MACF,MAAMgB,eAAe,GAAGhB,QAAQ,CAACgB,eAAe;MAChD,MAAM0C,YAAY,GAAG,CAAA1C,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE2C,IAAI,KAAI3D,QAAQ,CAAC0D,YAAY;MACnE,MAAMzC,MAAM,GAAGD,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEC,MAAM;;MAEtC;MACA,IAAIyC,YAAY,KAAK,uBAAuB,IAAIA,YAAY,KAAK,WAAW,IAAIA,YAAY,KAAK,YAAY,EAAE;QAC7G1H,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEyH,YAAY,CAAC;QAC1D1H,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE+E,eAAe,CAAC;;QAEhD;QACA,IAAIA,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAE6E,YAAY,IAAI5G,KAAK,CAACC,OAAO,CAAC8B,eAAe,CAAC6E,YAAY,CAAC,EAAE;UAChF,MAAMC,UAAU,GAAG,EAAE;UACrB9E,eAAe,CAAC6E,YAAY,CAACN,OAAO,CAAC,CAACQ,WAAW,EAAE9F,KAAK,KAAK;YAC3D,MAAM+F,SAAS,GAAGD,WAAW,CAAC9E,MAAM;YACpC,IAAI+E,SAAS,EAAE;cACbF,UAAU,CAACH,IAAI,CAAC,IAAI1F,KAAK,GAAG,CAAC,KAAK+F,SAAS,EAAE,CAAC;YAChD,CAAC,MAAM;cACLF,UAAU,CAACH,IAAI,CAAC,IAAI1F,KAAK,GAAG,CAAC,SAAS,CAAC;YACzC;UACF,CAAC,CAAC;UACF,OAAO6F,UAAU,CAACG,IAAI,CAAC,IAAI,CAAC;QAC9B;;QAEA;QACA,IAAIhF,MAAM,EAAE;UACV,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;YAC9B,IAAI;cACF,MAAMiF,YAAY,GAAGxF,IAAI,CAAC8C,KAAK,CAACvC,MAAM,CAAC;cACvC,IAAI,OAAOiF,YAAY,KAAK,QAAQ,IAAIA,YAAY,KAAK,IAAI,EAAE;gBAC7D,MAAMC,aAAa,GAAG7J,MAAM,CAAC8J,OAAO,CAACF,YAAY,CAAC;gBAClD,OAAOC,aAAa,CAACpG,GAAG,CAAC,CAAC,CAACyF,GAAG,EAAEa,KAAK,CAAC,KAAK,GAAGb,GAAG,IAAIa,KAAK,EAAE,CAAC,CAACJ,IAAI,CAAC,IAAI,CAAC;cAC1E;YACF,CAAC,CAAC,OAAOK,CAAC,EAAE;cACV;cACA,OAAOrF,MAAM;YACf;UACF,CAAC,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;YACxD,MAAMkF,aAAa,GAAG7J,MAAM,CAAC8J,OAAO,CAACnF,MAAM,CAAC;YAC5C,OAAOkF,aAAa,CAACpG,GAAG,CAAC,CAAC,CAACyF,GAAG,EAAEa,KAAK,CAAC,KAAK,GAAGb,GAAG,IAAIa,KAAK,EAAE,CAAC,CAACJ,IAAI,CAAC,IAAI,CAAC;UAC1E;QACF;QAEA,OAAO,OAAO;MAChB;MAEA,QAAQvC,YAAY;QAClB,KAAK,eAAe;QACpB,KAAK,iBAAiB;UACpB,IAAIzE,KAAK,CAACC,OAAO,CAAC+B,MAAM,CAAC,EAAE;YACzB,OAAOA,MAAM,CAACgF,IAAI,CAAC,IAAI,CAAC;UAC1B;UACA,OAAOhF,MAAM,IAAI,OAAO;QAE1B,KAAK,YAAY;UACf,OAAOA,MAAM,KAAK,IAAI,GAAG,IAAI,GAAGA,MAAM,KAAK,KAAK,GAAG,IAAI,GAAG,OAAO;QAEnE,KAAK,eAAe;UAClB,IAAIhC,KAAK,CAACC,OAAO,CAAC+B,MAAM,CAAC,EAAE;YACzB,OAAOA,MAAM,CAACgF,IAAI,CAAC,IAAI,CAAC;UAC1B;UACA,OAAOhF,MAAM,IAAI,OAAO;QAE1B;UACE,OAAOA,MAAM,GAAGuB,MAAM,CAACvB,MAAM,CAAC,GAAG,OAAO;MAC5C;IACF,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACd1C,OAAO,CAAC0C,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAO,QAAQ;IACjB;EACF,CAAC;;EAED;EACA,MAAM6H,2BAA2B,GAAIvG,QAAQ,IAAK;IAChD,IAAI;MACF,MAAMgB,eAAe,GAAGhB,QAAQ,CAACgB,eAAe;MAChD,MAAM0C,YAAY,GAAG,CAAA1C,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE2C,IAAI,KAAI3D,QAAQ,CAAC0D,YAAY;MACnE,MAAMzC,MAAM,GAAGD,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEC,MAAM;MAEtC,QAAQyC,YAAY;QAClB,KAAK,eAAe;QACpB,KAAK,iBAAiB;UACpB;UACA,MAAMQ,OAAO,GAAG,CAAAlD,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkD,OAAO,KAAIlE,QAAQ,CAACkE,OAAO;UAE5D,IAAIA,OAAO,IAAIjF,KAAK,CAACC,OAAO,CAACgF,OAAO,CAAC,IAAIjD,MAAM,EAAE;YAC/C;YACA,MAAMuF,WAAW,GAAGvF,MAAM,CAACwF,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;YAC/C,IAAID,WAAW,IAAI,CAAC,IAAIA,WAAW,GAAGtC,OAAO,CAAC9H,MAAM,EAAE;cACpD,MAAMsK,oBAAoB,GAAGxC,OAAO,CAACsC,WAAW,CAAC;cACjD,oBACEtL,OAAA;gBAAAgI,QAAA,gBACEhI,OAAA;kBAAAgI,QAAA,GAASjC,MAAM,EAAC,IAAE;gBAAA;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3B7H,OAAA,CAACN,mBAAmB;kBAAC8H,WAAW,EAAEgE;gBAAqB;kBAAA9D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAEV;UACF;;UAEA;UACA,IAAI9D,KAAK,CAACC,OAAO,CAAC+B,MAAM,CAAC,EAAE;YACzB,MAAM0F,UAAU,GAAG1F,MAAM,CAACgF,IAAI,CAAC,IAAI,CAAC;YACpC,IAAIU,UAAU,CAAClE,QAAQ,CAAC,GAAG,CAAC,EAAE;cAC5B,oBAAOvH,OAAA,CAACN,mBAAmB;gBAAC8H,WAAW,EAAEiE;cAAW;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YACzD;YACA,OAAO4D,UAAU;UACnB;UACA,IAAI1F,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACwB,QAAQ,CAAC,GAAG,CAAC,EAAE;YAChE,oBAAOvH,OAAA,CAACN,mBAAmB;cAAC8H,WAAW,EAAEzB;YAAO;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UACrD;UACA,OAAO9B,MAAM,IAAI,OAAO;QAE1B,KAAK,YAAY;UACf,OAAOA,MAAM,KAAK,IAAI,GAAG,IAAI,GAAGA,MAAM,KAAK,KAAK,GAAG,IAAI,GAAG,OAAO;QAEnE,KAAK,eAAe;UAClB,IAAIhC,KAAK,CAACC,OAAO,CAAC+B,MAAM,CAAC,EAAE;YACzB,MAAM0F,UAAU,GAAG1F,MAAM,CAACgF,IAAI,CAAC,IAAI,CAAC;YACpC;YACA,IAAIU,UAAU,CAAClE,QAAQ,CAAC,GAAG,CAAC,EAAE;cAC5B,oBAAOvH,OAAA,CAACN,mBAAmB;gBAAC8H,WAAW,EAAEiE;cAAW;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YACzD;YACA,OAAO4D,UAAU;UACnB;UACA;UACA,IAAI1F,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACwB,QAAQ,CAAC,GAAG,CAAC,EAAE;YAChE,oBAAOvH,OAAA,CAACN,mBAAmB;cAAC8H,WAAW,EAAEzB;YAAO;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UACrD;UACA,OAAO9B,MAAM,IAAI,OAAO;QAE1B;UACE,IAAIA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACwB,QAAQ,CAAC,GAAG,CAAC,EAAE;YAChE,oBAAOvH,OAAA,CAACN,mBAAmB;cAAC8H,WAAW,EAAEzB;YAAO;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UACrD;UACA,OAAO9B,MAAM,GAAGuB,MAAM,CAACvB,MAAM,CAAC,GAAG,OAAO;MAC5C;IACF,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACd,OAAO,QAAQ;IACjB;EACF,CAAC;;EAED;EACA,MAAMkI,4BAA4B,GAAG9M,WAAW,CAAC,CAAC+M,eAAe,EAAEpD,UAAU,EAAEqD,cAAc,EAAEC,cAAc,GAAG,IAAI,KAAK;IACvH,IAAI;MACF,IAAIzE,OAAO,GAAGuE,eAAe;MAC7B,IAAIG,UAAU,GAAG,CAAC;;MAElB;MACA1E,OAAO,GAAGA,OAAO,CAAC2E,OAAO,CAAC,KAAK,EAAE,MAAM;QACrC,MAAMC,OAAO,GAAG,SAASF,UAAU,EAAE;QACrC,MAAMpD,UAAU,GAAGH,UAAU,CAACyD,OAAO,CAAC,IAAI,EAAE;;QAE5C;QACA,IAAInG,aAAa,GAAG,EAAE;QACtB,IAAI9B,KAAK,CAACC,OAAO,CAAC4H,cAAc,CAAC,EAAE;UACjC/F,aAAa,GAAG+F,cAAc,CAACE,UAAU,CAAC,IAAI,EAAE;QAClD,CAAC,MAAM,IAAIF,cAAc,CAACI,OAAO,CAAC,EAAE;UAClCnG,aAAa,GAAG+F,cAAc,CAACI,OAAO,CAAC;QACzC;;QAEA;QACA;QACA,IAAI5G,SAAS,GAAG,KAAK;QACrB,IAAIyG,cAAc,IAAIA,cAAc,CAACzG,SAAS,KAAK2D,SAAS,EAAE;UAC5D;UACA3D,SAAS,GAAGyG,cAAc,CAACzG,SAAS;QACtC,CAAC,MAAM;UACL;UACAA,SAAS,GAAGsD,UAAU,CAACuD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAKrG,aAAa,CAACoG,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpF;QACA,MAAMC,UAAU,GAAG/G,SAAS,GAAG,GAAG,GAAG,GAAG;QACxC,MAAMgH,WAAW,GAAGhH,SAAS,GAAG,SAAS,GAAG,SAAS;QAErD0G,UAAU,EAAE;;QAEZ;QACA,IAAIpD,UAAU,EAAE;UACd,OAAO,mGAAmG0D,WAAW;AAC/H,cAAc1D,UAAU,wBAAwB0D,WAAW,2CAA2CD,UAAU;AAChH,kBAAkB;QACV,CAAC,MAAM;UACL;UACA,OAAO;AACjB;AACA,kBAAkB;QACV;MACF,CAAC,CAAC;MAEF,oBAAOnM,OAAA,CAACN,mBAAmB;QAAC8H,WAAW,EAAEJ;MAAQ;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACtD,CAAC,CAAC,OAAOrE,KAAK,EAAE;MACd1C,OAAO,CAAC0C,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,OAAO,MAAM;IACf;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAI1B,aAAa,EAAE;IACjB,oBACE9B,OAAA;MAAKqM,SAAS,EAAC,+BAA+B;MAAArE,QAAA,eAC5ChI,OAAA;QAAKqM,SAAS,EAAC,2BAA2B;QAAArE,QAAA,gBACxChI,OAAA;UAAK8H,KAAK,EAAE;YAAEwE,QAAQ,EAAE,MAAM;YAAE3C,YAAY,EAAE,MAAM;YAAE5B,SAAS,EAAE;UAAS,CAAE;UAAAC,QAAA,EAAC;QAAW;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9F7H,OAAA;UAAKqM,SAAS,EAAC;QAAY;UAAA3E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAM0E,YAAY,GAAGzI,cAAc,CAAC5C,MAAM,GAAG,CAAC;;EAE9C;EACA,MAAMsL,oBAAoB,GAAGA,CAAClM,SAAS,EAAED,WAAW,KAAK;IACvD,IAAI,CAACC,SAAS,CAACY,MAAM,EAAE;MACrB,OAAO;QAAEuL,YAAY,EAAE,CAAC;QAAEC,UAAU,EAAE,CAAC;QAAEC,aAAa,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAE,CAAC;IAC1E;IAEA,IAAIH,YAAY,GAAG,CAAC;IACpB,IAAIE,aAAa,GAAG,CAAC;IAErBrM,SAAS,CAAC+J,OAAO,CAACvF,QAAQ,IAAI;MAC5B,MAAM4D,UAAU,GAAGrI,WAAW,CAACyE,QAAQ,CAACV,EAAE,CAAC;MAC3C,IAAIsE,UAAU,EAAE;QACdiE,aAAa,EAAE;QACf,IAAIjE,UAAU,CAACtD,SAAS,EAAE;UACxBqH,YAAY,EAAE;QAChB;MACF;IACF,CAAC,CAAC;IAEF,OAAO;MACLA,YAAY;MACZC,UAAU,EAAEpM,SAAS,CAACY,MAAM;MAC5ByL,aAAa;MACb;MACAC,QAAQ,EAAED,aAAa,GAAG,CAAC,GAAGF,YAAY,GAAGE,aAAa,GAAG;IAC/D,CAAC;EACH,CAAC;EAED,MAAME,WAAW,GAAGL,oBAAoB,CAAC1I,cAAc,EAAEG,gBAAgB,CAAC;;EAE1E;EACA,MAAM6I,KAAK,GAAG;IACZC,cAAc,EAAEF,WAAW,CAACH,UAAU,GAAG,CAAC,GAAIG,WAAW,CAACF,aAAa,GAAGE,WAAW,CAACH,UAAU,GAAI,GAAG,GAAG,CAAC;IAC3GM,WAAW,EAAEH,WAAW,CAACD,QAAQ,IAAI,CAAC;IACtCK,iBAAiB,EAAEJ,WAAW,CAACF,aAAa,IAAI,CAAC;IACjDO,cAAc,EAAEL,WAAW,CAACH,UAAU,IAAI;EAC5C,CAAC;;EAED;EACA,MAAMS,YAAY,GAAGL,KAAK,CAACE,WAAW,IAAI,GAAG,GAAG,KAAK,GAAGF,KAAK,CAACE,WAAW,IAAI,GAAG,GAAG,MAAM,GAAG,KAAK;EACjG,MAAMI,WAAW,GAAGN,KAAK,CAACE,WAAW,IAAI,GAAG,GAAG,IAAI,GAAGF,KAAK,CAACE,WAAW,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI;EAC5F,MAAMK,YAAY,GAAGP,KAAK,CAACE,WAAW,IAAI,GAAG,GAAG,SAAS,GAAGF,KAAK,CAACE,WAAW,IAAI,GAAG,GAAG,SAAS,GAAG,SAAS;EAE5G,oBACEhN,OAAA;IAAKqM,SAAS,EAAC,+BAA+B;IAAArE,QAAA,gBAE5ChI,OAAA;MACEqM,SAAS,EAAC,qBAAqB;MAC/BvE,KAAK,EAAE;QACLwF,eAAe,EAAE,OAAOC,OAAO,CAACC,GAAG,CAACC,UAAU,kBAAkB;QAChEC,cAAc,EAAE,OAAO;QACvBC,kBAAkB,EAAE,QAAQ;QAC5BC,gBAAgB,EAAE;MACpB,CAAE;MAAA5F,QAAA,gBAGFhI,OAAA;QAAKqM,SAAS,EAAC,uBAAuB;QAAArE,QAAA,gBAEpChI,OAAA;UAAKqM,SAAS,EAAC,uBAAuB;UAAArE,QAAA,gBACpChI,OAAA;YAAKqM,SAAS,EAAC,eAAe;YAAArE,QAAA,eAC5BhI,OAAA;cAAM8H,KAAK,EAAE;gBAAEwE,QAAQ,EAAE;cAAO,CAAE;cAAAtE,QAAA,EAAEoF;YAAW;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACN7H,OAAA,CAACC,KAAK;YAAC4N,KAAK,EAAE,CAAE;YAACxB,SAAS,EAAC,cAAc;YAAArE,QAAA,EACtCmF;UAAY;YAAAzF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN7H,OAAA;UAAKqM,SAAS,EAAC,0BAA0B;UAAArE,QAAA,eACvChI,OAAA,CAACC,KAAK;YAAC4N,KAAK,EAAE,CAAE;YAACxB,SAAS,EAAC,sBAAsB;YAAArE,QAAA,EAC9C,CAAArE,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAE3C,IAAI,KAAI;UAAK;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN7H,OAAA;UAAKqM,SAAS,EAAC,qBAAqB;UAAArE,QAAA,gBAClChI,OAAA;YAAKqM,SAAS,EAAC,oBAAoB;YAAArE,QAAA,eACjChI,OAAA;cACEqM,SAAS,EAAC,eAAe;cACzBvE,KAAK,EAAE;gBACLgG,KAAK,EAAE,GAAGvJ,IAAI,CAACwJ,KAAK,CAACjB,KAAK,CAACE,WAAW,GAAG,GAAG,CAAC,GAAG;gBAChD1D,eAAe,EAAE+D;cACnB;YAAE;cAAA3F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN7H,OAAA;YAAMqM,SAAS,EAAC,eAAe;YAAArE,QAAA,GAAEzD,IAAI,CAACwJ,KAAK,CAACjB,KAAK,CAACE,WAAW,GAAG,GAAG,CAAC,EAAC,GAAC;UAAA;YAAAtF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLnH,aAAa,iBACZV,OAAA;QAAK8H,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEiG,MAAM,EAAE,QAAQ;UAAEhE,KAAK,EAAE,MAAM;UAAEsC,QAAQ,EAAE;QAAO,CAAE;QAAAtE,QAAA,EAAC;MAExF;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN,eAGD7H,OAAA;QAAK8H,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEiG,MAAM,EAAE;QAAS,CAAE;QAAAhG,QAAA,eACpDhI,OAAA,CAAClB,MAAM;UACLuN,SAAS,EAAC,2BAA2B;UACrCvE,KAAK,EAAE;YACLmG,UAAU,EAAE,mDAAmD;YAC/DnE,MAAM,EAAE,mBAAmB;YAC3BE,KAAK,EAAE,SAAS;YAChBD,UAAU,EAAE,KAAK;YACjBF,YAAY,EAAE,MAAM;YACpBD,OAAO,EAAE,UAAU;YACnBsE,MAAM,EAAE,MAAM;YACd5B,QAAQ,EAAE,MAAM;YAChB6B,SAAS,EAAE,oCAAoC;YAC/CC,UAAU,EAAE;UACd,CAAE;UACFC,YAAY,EAAGjD,CAAC,IAAK;YACnBA,CAAC,CAACkD,MAAM,CAACxG,KAAK,CAACmG,UAAU,GAAG,mDAAmD;YAC/E7C,CAAC,CAACkD,MAAM,CAACxG,KAAK,CAACyG,SAAS,GAAG,kBAAkB;YAC7CnD,CAAC,CAACkD,MAAM,CAACxG,KAAK,CAACqG,SAAS,GAAG,oCAAoC;UACjE,CAAE;UACFK,YAAY,EAAGpD,CAAC,IAAK;YACnBA,CAAC,CAACkD,MAAM,CAACxG,KAAK,CAACmG,UAAU,GAAG,mDAAmD;YAC/E7C,CAAC,CAACkD,MAAM,CAACxG,KAAK,CAACyG,SAAS,GAAG,eAAe;YAC1CnD,CAAC,CAACkD,MAAM,CAACxG,KAAK,CAACqG,SAAS,GAAG,oCAAoC;UACjE,CAAE;UACFM,OAAO,EAAEpI,uBAAwB;UAAA2B,QAAA,EAClC;QAED;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN7H,OAAA;QAAKqM,SAAS,EAAC,qBAAqB;QAAArE,QAAA,GAEjCuE,YAAY,iBACXvM,OAAA;UAAKqM,SAAS,EAAC,yBAAyB;UAAArE,QAAA,gBACtChI,OAAA;YAAMqM,SAAS,EAAC,eAAe;YAAArE,QAAA,EAAC;UAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAC3C/D,cAAc,CAACe,GAAG,CAAC,CAAC6J,CAAC,EAAE3J,KAAK,KAAK;YAAA,IAAA4J,qBAAA;YAChC,MAAM1J,cAAc,GAAGhB,gBAAgB,EAAA0K,qBAAA,GAAC7K,cAAc,CAACiB,KAAK,CAAC,cAAA4J,qBAAA,uBAArBA,qBAAA,CAAuBvK,EAAE,CAAC;YAClE,MAAMgB,SAAS,GAAGH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEG,SAAS;YAC3C,MAAMD,SAAS,GAAGF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEE,SAAS;YAC3C,oBACEnF,OAAA;cAEEqM,SAAS,EAAE,0BACTlH,SAAS,GAAG,SAAS,GAAIC,SAAS,GAAG,SAAS,GAAG,OAAQ,EACxD;cACHqJ,OAAO,EAAEA,CAAA,KAAMvI,mBAAmB,CAACnB,KAAK,CAAE;cAC1C+C,KAAK,EAAE;gBAAE8G,MAAM,EAAE;cAAU,CAAE;cAC7B/H,KAAK,EAAC,kDAAU;cAAAmB,QAAA,EAEfjD,KAAK,GAAG;YAAC,GARLA,KAAK;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASN,CAAC;UAEX,CAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAGD7H,OAAA;UAAKqM,SAAS,EAAC,qBAAqB;UAAArE,QAAA,GACjCzH,SAAS,iBACRP,OAAA,CAAClB,MAAM;YACLuN,SAAS,EAAC,gBAAgB;YAC1BoC,OAAO,EAAElO,SAAU;YACnBuH,KAAK,EAAE;cACLmG,UAAU,EAAE,SAAS;cACrBnE,MAAM,EAAE,mBAAmB;cAC3BE,KAAK,EAAE,SAAS;cAChBD,UAAU,EAAE,KAAK;cACjBF,YAAY,EAAE,MAAM;cACpBD,OAAO,EAAE,UAAU;cACnBsE,MAAM,EAAE,MAAM;cACd5B,QAAQ,EAAE,MAAM;cAChBwB,KAAK,EAAE,OAAO;cACdM,UAAU,EAAE;YACd,CAAE;YACFC,YAAY,EAAGjD,CAAC,IAAK;cACnBA,CAAC,CAACkD,MAAM,CAACxG,KAAK,CAACmG,UAAU,GAAG,SAAS;cACrC7C,CAAC,CAACkD,MAAM,CAACxG,KAAK,CAACyG,SAAS,GAAG,kBAAkB;cAC7CnD,CAAC,CAACkD,MAAM,CAACxG,KAAK,CAACqG,SAAS,GAAG,oCAAoC;YACjE,CAAE;YACFK,YAAY,EAAGpD,CAAC,IAAK;cACnBA,CAAC,CAACkD,MAAM,CAACxG,KAAK,CAACmG,UAAU,GAAG,SAAS;cACrC7C,CAAC,CAACkD,MAAM,CAACxG,KAAK,CAACyG,SAAS,GAAG,eAAe;cAC1CnD,CAAC,CAACkD,MAAM,CAACxG,KAAK,CAACqG,SAAS,GAAG,MAAM;YACnC,CAAE;YAAAnG,QAAA,EACH;UAED;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eACD7H,OAAA,CAAClB,MAAM;YACL2J,IAAI,EAAC,SAAS;YACdoG,IAAI,EAAC,OAAO;YACZxC,SAAS,EAAC,gBAAgB;YAC1BoC,OAAO,EAAEA,CAAA,KAAMK,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;YAAAhH,QAAA,EACtC;UAED;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7H,OAAA,CAACF,mBAAmB;MAClBmP,OAAO,EAAEzN,YAAa;MACtB0N,OAAO,EAAE9I,gBAAiB;MAC1B9E,gBAAgB,EAAEA,gBAAiB;MACnC2G,mBAAmB,EAAEA,mBAAoB;MACzCyC,mBAAmB,EAAEW,2BAA4B;MACjDK,4BAA4B,EAAEA;IAA6B;MAAAhE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC,eAGF7H,OAAA,CAACjB,KAAK;MACJ8H,KAAK,EAAE,IAAK;MACZsI,IAAI,EAAEjN,wBAAyB;MAC/BkN,QAAQ,EAAEnI,4BAA6B;MACvCoI,MAAM,EAAE,IAAK;MACbvB,KAAK,EAAE,IAAK;MACZwB,QAAQ;MACRC,QAAQ,EAAE,KAAM;MAChBlD,SAAS,EAAC,0BAA0B;MACpCmD,MAAM,EAAE;QACNC,IAAI,EAAE;UAAE7F,OAAO,EAAE;QAAE;MACrB,CAAE;MAAA5B,QAAA,gBAGFhI,OAAA;QAAKqM,SAAS,EAAC,wBAAwB;QAACvE,KAAK,EAAE;UAC7CmG,UAAU,EAAE,oBAAoB;UAChCrE,OAAO,EAAE,GAAG;UACZ8F,QAAQ,EAAE,UAAU;UACpB5B,KAAK,EAAE;QACT,CAAE;QAAA9F,QAAA,eACAhI,OAAA;UAAK8H,KAAK,EAAE;YACVgG,KAAK,EAAE,MAAM;YACblE,OAAO,EAAE,WAAW;YACpB+F,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,YAAY;YAC5BH,QAAQ,EAAE;UACZ,CAAE;UAAA1H,QAAA,gBACAhI,OAAA;YAAKqM,SAAS,EAAC,oDAAoD;YAACvE,KAAK,EAAE;cACzEmG,UAAU,EAAE,SAAS;cACrBjE,KAAK,EAAE,SAAS;cAChBJ,OAAO,EAAE,UAAU;cACnBC,YAAY,EAAE,aAAa;cAC3ByC,QAAQ,EAAE,MAAM;cAChBvC,UAAU,EAAE,KAAK;cACjBE,WAAW,EAAE;YACf,CAAE;YAAAjC,QAAA,EAAC;UAEH;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN7H,OAAA;YAAKqM,SAAS,EAAC,yBAAyB;YAACoC,OAAO,EAAExH,4BAA6B;YAACa,KAAK,EAAE;cACrF8G,MAAM,EAAE,SAAS;cACjB5E,KAAK,EAAE,SAAS;cAChBsC,QAAQ,EAAE,MAAM;cAChB1C,OAAO,EAAE,KAAK;cACd8F,QAAQ,EAAE,UAAU;cACpBI,KAAK,EAAE,MAAM;cACbC,GAAG,EAAE,KAAK;cACVxB,SAAS,EAAE;YACb,CAAE;YAAAvG,QAAA,eACAhI,OAAA,CAACT,aAAa;cAAAmI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7H,OAAA;QAAKqM,SAAS,EAAC,0BAA0B;QAACvE,KAAK,EAAE;UAC/C6H,OAAO,EAAE,MAAM;UACfzB,MAAM,EAAE,OAAO;UACfD,UAAU,EAAE;QACd,CAAE;QAAAjG,QAAA,gBAEAhI,OAAA;UAAK8H,KAAK,EAAE;YACVgG,KAAK,EAAE,KAAK;YACZ6B,OAAO,EAAE,MAAM;YACfK,aAAa,EAAE,QAAQ;YACvB/B,UAAU,EAAE,SAAS;YACrByB,QAAQ,EAAE;UACZ,CAAE;UAAA1H,QAAA,eACAhI,OAAA;YAAK8H,KAAK,EAAE;cACVmI,IAAI,EAAE,CAAC;cACPP,QAAQ,EAAE,UAAU;cACpBQ,SAAS,EAAE,OAAO;cAClBP,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE;YAClB,CAAE;YAAA7H,QAAA,GACClF,aAAa,gBACZ9C,OAAA;cAAK8H,KAAK,EAAE;gBACVkC,KAAK,EAAE,SAAS;gBAChBsC,QAAQ,EAAE,MAAM;gBAChBvE,SAAS,EAAE;cACb,CAAE;cAAAC,QAAA,gBACAhI,OAAA,CAACV,kBAAkB;gBAACwI,KAAK,EAAE;kBAAEwE,QAAQ,EAAE,MAAM;kBAAE3C,YAAY,EAAE;gBAAO;cAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzE7H,OAAA;gBAAAgI,QAAA,EAAK;cAAS;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,GACJzF,YAAY,gBACdpC,OAAA,CAACR,WAAW;cACV2Q,GAAG,EAAE/N,YAAY,CAAC0E,QAAS;cAC3BgH,KAAK,EAAC,MAAM;cACZI,MAAM,EAAC,MAAM;cACb5L,OAAO,EAAEA,OAAQ;cACjBE,YAAY,EAAEA,YAAa;cAC3B4N,QAAQ,EAAE,IAAK;cACfC,KAAK,EAAEjO,YAAY,CAAC2E,aAAc;cAClCuJ,MAAM,EAAEA,CAAA,KAAM/N,UAAU,CAAC,IAAI,CAAE;cAC/BgO,OAAO,EAAEA,CAAA,KAAMhO,UAAU,CAAC,KAAK,CAAE;cACjCkF,OAAO,EAAGjE,KAAK,IAAK;gBAClB1C,OAAO,CAAC0C,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;gBAC/BvE,OAAO,CAACuE,KAAK,CAAC,QAAQ,CAAC;cACzB,CAAE;cACFgN,MAAM,EAAE;gBACNC,IAAI,EAAE;kBACJC,UAAU,EAAE;oBACV5I,KAAK,EAAE;sBAAEgG,KAAK,EAAE,MAAM;sBAAEI,MAAM,EAAE;oBAAO,CAAC;oBACxCyC,YAAY,EAAE,YAAY;oBAC1BC,uBAAuB,EAAE;kBAC3B;gBACF;cACF;YAAE;cAAAlJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAEF7H,OAAA;cAAK8H,KAAK,EAAE;gBACVkC,KAAK,EAAE,SAAS;gBAChBsC,QAAQ,EAAE,MAAM;gBAChBvE,SAAS,EAAE;cACb,CAAE;cAAAC,QAAA,gBACAhI,OAAA,CAACV,kBAAkB;gBAACwI,KAAK,EAAE;kBAAEwE,QAAQ,EAAE,MAAM;kBAAE3C,YAAY,EAAE;gBAAO;cAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzE7H,OAAA;gBAAAgI,QAAA,EAAK;cAAI;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CACN,EAGAzF,YAAY,iBACXpC,OAAA;cAAK8H,KAAK,EAAE;gBACV4H,QAAQ,EAAE,UAAU;gBACpBmB,MAAM,EAAE,MAAM;gBACdf,KAAK,EAAE,MAAM;gBACbH,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBkB,GAAG,EAAE,KAAK;gBACV7C,UAAU,EAAE,oBAAoB;gBAChCrE,OAAO,EAAE,UAAU;gBACnBC,YAAY,EAAE,MAAM;gBACpBG,KAAK,EAAE,SAAS;gBAChBsC,QAAQ,EAAE;cACZ,CAAE;cAAAtE,QAAA,gBACAhI,OAAA;gBAAAgI,QAAA,EAAM;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChB7H,OAAA;gBACEmL,KAAK,EAAE3I,YAAa;gBACpBuO,QAAQ,EAAG3F,CAAC,IAAK3I,eAAe,CAACuO,UAAU,CAAC5F,CAAC,CAACkD,MAAM,CAACnD,KAAK,CAAC,CAAE;gBAC7DrD,KAAK,EAAE;kBACLmG,UAAU,EAAE,aAAa;kBACzBnE,MAAM,EAAE,MAAM;kBACdE,KAAK,EAAE,SAAS;kBAChBsC,QAAQ,EAAE,MAAM;kBAChBsC,MAAM,EAAE,SAAS;kBACjBqC,OAAO,EAAE;gBACX,CAAE;gBAAAjJ,QAAA,gBAEFhI,OAAA;kBAAQmL,KAAK,EAAE,GAAI;kBAACrD,KAAK,EAAE;oBAAEmG,UAAU,EAAE,MAAM;oBAAEjE,KAAK,EAAE;kBAAO,CAAE;kBAAAhC,QAAA,EAAC;gBAAI;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/E7H,OAAA;kBAAQmL,KAAK,EAAE,IAAK;kBAACrD,KAAK,EAAE;oBAAEmG,UAAU,EAAE,MAAM;oBAAEjE,KAAK,EAAE;kBAAO,CAAE;kBAAAhC,QAAA,EAAC;gBAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjF7H,OAAA;kBAAQmL,KAAK,EAAE,CAAE;kBAACrD,KAAK,EAAE;oBAAEmG,UAAU,EAAE,MAAM;oBAAEjE,KAAK,EAAE;kBAAO,CAAE;kBAAAhC,QAAA,EAAC;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3E7H,OAAA;kBAAQmL,KAAK,EAAE,IAAK;kBAACrD,KAAK,EAAE;oBAAEmG,UAAU,EAAE,MAAM;oBAAEjE,KAAK,EAAE;kBAAO,CAAE;kBAAAhC,QAAA,EAAC;gBAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjF7H,OAAA;kBAAQmL,KAAK,EAAE,GAAI;kBAACrD,KAAK,EAAE;oBAAEmG,UAAU,EAAE,MAAM;oBAAEjE,KAAK,EAAE;kBAAO,CAAE;kBAAAhC,QAAA,EAAC;gBAAI;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/E7H,OAAA;kBAAQmL,KAAK,EAAE,CAAE;kBAACrD,KAAK,EAAE;oBAAEmG,UAAU,EAAE,MAAM;oBAAEjE,KAAK,EAAE;kBAAO,CAAE;kBAAAhC,QAAA,EAAC;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN7H,OAAA;UAAK8H,KAAK,EAAE;YACVgG,KAAK,EAAE,KAAK;YACZG,UAAU,EAAE,SAAS;YACrBrE,OAAO,EAAE,MAAM;YACf+F,OAAO,EAAE,MAAM;YACfK,aAAa,EAAE,QAAQ;YACvBc,GAAG,EAAE;UACP,CAAE;UAAA9I,QAAA,gBACAhI,OAAA;YAAAgI,QAAA,eACEhI,OAAA;cAAI8H,KAAK,EAAE;gBACTkG,MAAM,EAAE,YAAY;gBACpBhE,KAAK,EAAE,SAAS;gBAChBsC,QAAQ,EAAE,MAAM;gBAChBvC,UAAU,EAAE;cACd,CAAE;cAAA/B,QAAA,EAAC;YAEH;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGN7H,OAAA;YAAKqM,SAAS,EAAC,sBAAsB;YAACvE,KAAK,EAAE;cAAEmI,IAAI,EAAE,CAAC;cAAEiB,SAAS,EAAE;YAAO,CAAE;YAAAlJ,QAAA,EACzEtF,SAAS,CAACmC,GAAG,CAAC,CAAC+B,KAAK,EAAE7B,KAAK,kBAC1B/E,OAAA;cAEEyO,OAAO,EAAEA,CAAA,KAAMvH,iBAAiB,CAACnC,KAAK,CAAE;cACxCsH,SAAS,EAAE,mBAAmBzJ,iBAAiB,KAAKmC,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC5E+C,KAAK,EAAE;gBACL8B,OAAO,EAAE,MAAM;gBACfoE,MAAM,EAAE,WAAW;gBACnBC,UAAU,EAAErL,iBAAiB,KAAKmC,KAAK,GAAG,SAAS,GAAG,aAAa;gBACnE8E,YAAY,EAAE,KAAK;gBACnB+E,MAAM,EAAE,SAAS;gBACjBR,UAAU,EAAE,UAAU;gBACtBuB,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBkB,GAAG,EAAE;cACP,CAAE;cAAA9I,QAAA,gBAGFhI,OAAA;gBAAK8H,KAAK,EAAE;kBACVmG,UAAU,EAAE,SAAS;kBACrBjE,KAAK,EAAE,SAAS;kBAChBsC,QAAQ,EAAE,MAAM;kBAChBvC,UAAU,EAAE,KAAK;kBACjBH,OAAO,EAAE,SAAS;kBAClBC,YAAY,EAAE,KAAK;kBACnBsH,QAAQ,EAAE,MAAM;kBAChBpJ,SAAS,EAAE;gBACb,CAAE;gBAAAC,QAAA,EACCjD,KAAK,GAAG;cAAC;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eAGN7H,OAAA;gBAAK8H,KAAK,EAAE;kBAAEmI,IAAI,EAAE,CAAC;kBAAEkB,QAAQ,EAAE;gBAAE,CAAE;gBAAAnJ,QAAA,gBACnChI,OAAA;kBAAK8H,KAAK,EAAE;oBACViC,UAAU,EAAE,KAAK;oBACjBC,KAAK,EAAE,SAAS;oBAChBsC,QAAQ,EAAE,MAAM;oBAChB3C,YAAY,EAAE,KAAK;oBACnByH,QAAQ,EAAE,QAAQ;oBAClBC,YAAY,EAAE,UAAU;oBACxBC,UAAU,EAAE;kBACd,CAAE;kBAAAtJ,QAAA,EACCpB,KAAK,CAACC,KAAK,CAACkF,OAAO,CAAC,UAAU,EAAE,EAAE;gBAAC;kBAAArE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACN7H,OAAA;kBAAK8H,KAAK,EAAE;oBACV6H,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBkB,GAAG,EAAE,MAAM;oBACXxE,QAAQ,EAAE,MAAM;oBAChBtC,KAAK,EAAE;kBACT,CAAE;kBAAAhC,QAAA,EACCpB,KAAK,CAAChC,eAAe,iBACpB5E,OAAA;oBAAAgI,QAAA,GACGzD,IAAI,CAACC,KAAK,CAACoC,KAAK,CAAChC,eAAe,GAAG,EAAE,CAAC,CAAC2M,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,GAAC,EAAC,CAAC5K,KAAK,CAAChC,eAAe,GAAG,EAAE,EAAE2M,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;kBAAA;oBAAA9J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1H;gBACP;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAvDDjB,KAAK,CAACxC,EAAE;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwDV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAChH,EAAA,CA9kCIX,YAAY;AAAAuR,EAAA,GAAZvR,YAAY;AAglClBA,YAAY,CAACwR,SAAS,GAAG;EACvBvR,cAAc,EAAEV,SAAS,CAACkS,KAAK,CAAC;IAC9BvN,EAAE,EAAE3E,SAAS,CAACmS,MAAM;IACpB5Q,IAAI,EAAEvB,SAAS,CAACoS,MAAM;IACtBC,WAAW,EAAErS,SAAS,CAACoS,MAAM;IAC7BE,WAAW,EAAEtS,SAAS,CAACoS;EACzB,CAAC,CAAC;EACFzR,QAAQ,EAAEX,SAAS,CAACmS,MAAM;EAC1BvR,WAAW,EAAEZ,SAAS,CAACuS,MAAM;EAC7B1R,SAAS,EAAEb,SAAS,CAACwS,KAAK;EAC1B1R,SAAS,EAAEd,SAAS,CAACyS,IAAI;EACzB1R,eAAe,EAAEf,SAAS,CAACyS,IAAI;EAC/BzR,SAAS,EAAEhB,SAAS,CAACmS,MAAM;EAAE;EAC7BjR,aAAa,EAAElB,SAAS,CAACmS,MAAM;EAAE;EACjChR,WAAW,EAAEnB,SAAS,CAACmS,MAAM,CAAC;AAChC,CAAC;AAED,eAAe1R,YAAY;AAAC,IAAAuR,EAAA;AAAAU,YAAA,CAAAV,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}