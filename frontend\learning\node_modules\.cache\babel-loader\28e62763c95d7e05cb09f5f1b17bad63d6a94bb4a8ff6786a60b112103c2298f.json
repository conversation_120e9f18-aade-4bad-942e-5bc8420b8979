{"ast": null, "code": "/**\r\n * 学习模块状态管理Reducer\r\n */\n\n// 学习阶段常量\nexport const LEARNING_STAGES = {\n  INTRO: 'INTRO',\n  VIDEO: 'VIDEO',\n  QUESTIONS: 'QUESTIONS',\n  RESULT: 'RESULT',\n  REVIEW: 'REVIEW' // 新增：查看题目阶段\n};\n\n// 初始状态\nexport const initialState = {\n  knowledgePoint: null,\n  questions: [],\n  // 题目列表\n  currentStage: 'INTRO',\n  // 当前学习阶段\n  currentQuestionIndex: 0,\n  // 当前题目索引\n  userAnswers: {},\n  userSelection: {},\n  // 用户当前选择但尚未提交的答案\n  answerVerified: false,\n  // 标记当前题目是否已验证答案\n  showingAnswer: false,\n  // 标记是否正在显示答案和解析\n  finalResult: null,\n  testStartTime: null,\n  // 测试开始时间\n  testEndTime: null,\n  // 测试结束时间\n  loading: false,\n  error: null\n};\n\n/**\r\n * 学习模块Reducer\r\n * \r\n * @param {Object} state 当前状态\r\n * @param {Object} action 动作对象\r\n * @returns {Object} 新状态\r\n */\nexport const learningReducer = (state, action) => {\n  var _state$knowledgePoint5, _state$knowledgePoint6, _state$knowledgePoint7, _state$knowledgePoint8, _state$questions2;\n  switch (action.type) {\n    case 'FETCH_START':\n      return {\n        ...state,\n        loading: true,\n        error: null\n      };\n    case 'FETCH_SUCCESS':\n      // 确保数据结构正确，安全处理undefined payload\n      const payload = action.payload || {};\n      const knowledgePoint = payload.knowledgePoint || payload;\n      const questions = payload.questions;\n      console.log('Reducer处理的数据:', {\n        knowledgePoint,\n        questions,\n        hasQuestions: !!questions\n      });\n\n      // 如果payload为空对象，只更新loading状态，不重置数据\n      if (!knowledgePoint && !questions) {\n        console.log('Reducer - 空payload，仅更新loading状态');\n        return {\n          ...state,\n          loading: false,\n          error: null\n        };\n      }\n      return {\n        ...state,\n        loading: false,\n        error: null,\n        knowledgePoint: knowledgePoint || state.knowledgePoint,\n        questions: questions ? Array.isArray(questions) ? questions : [] : state.questions,\n        currentStage: knowledgePoint ? 'INTRO' : state.currentStage\n      };\n    case 'FETCH_ERROR':\n      return {\n        ...state,\n        loading: false,\n        error: action.payload\n      };\n    case 'FETCH_END':\n      // 仅结束加载状态，不重置数据\n      return {\n        ...state,\n        loading: false,\n        error: null\n      };\n    case 'RESET_ERROR':\n      return {\n        ...state,\n        error: null\n      };\n    case 'NEXT_STAGE':\n      const stages = Object.values(LEARNING_STAGES);\n      const currentIndex = stages.indexOf(state.currentStage);\n\n      // 其他阶段进入下一阶段\n      let nextStage = stages[currentIndex + 1];\n\n      // 如果从INTRO阶段进入下一阶段，检查是否有视频内容\n      if (state.currentStage === 'INTRO' && nextStage === 'VIDEO') {\n        var _state$knowledgePoint, _state$knowledgePoint2, _state$knowledgePoint3, _state$knowledgePoint4, _state$questions;\n        // 检查知识点是否有视频内容（videoCollectionId为空或null表示没有视频）\n        const hasVideo = ((_state$knowledgePoint = state.knowledgePoint) === null || _state$knowledgePoint === void 0 ? void 0 : _state$knowledgePoint.videoCollectionId) && state.knowledgePoint.videoCollectionId !== null && state.knowledgePoint.videoCollectionId !== '';\n        console.log('检查视频内容:', {\n          knowledgePointId: (_state$knowledgePoint2 = state.knowledgePoint) === null || _state$knowledgePoint2 === void 0 ? void 0 : _state$knowledgePoint2.id,\n          knowledgePointName: (_state$knowledgePoint3 = state.knowledgePoint) === null || _state$knowledgePoint3 === void 0 ? void 0 : _state$knowledgePoint3.name,\n          videoCollectionId: (_state$knowledgePoint4 = state.knowledgePoint) === null || _state$knowledgePoint4 === void 0 ? void 0 : _state$knowledgePoint4.videoCollectionId,\n          hasVideo: hasVideo,\n          questionsCount: ((_state$questions = state.questions) === null || _state$questions === void 0 ? void 0 : _state$questions.length) || 0\n        });\n        if (!hasVideo) {\n          console.log('知识点没有视频内容，跳过VIDEO阶段');\n          // 跳过VIDEO阶段，直接进入QUESTIONS阶段\n          nextStage = 'QUESTIONS';\n\n          // 如果也没有题目，则直接跳到RESULT阶段\n          if (!state.questions || state.questions.length === 0) {\n            console.log('也没有题目，直接跳到RESULT阶段');\n            nextStage = 'RESULT';\n          }\n        } else {\n          console.log('知识点有视频内容，正常进入VIDEO阶段');\n        }\n      }\n\n      // 如果下一阶段是QUESTIONS但没有题目，跳过到RESULT\n      if (nextStage === 'QUESTIONS' && (!state.questions || state.questions.length === 0)) {\n        nextStage = 'RESULT';\n      }\n\n      // 如果进入QUESTIONS阶段，记录开始时间\n      const newState = {\n        ...state,\n        currentStage: nextStage || state.currentStage\n      };\n      if (nextStage === 'QUESTIONS' && !state.testStartTime) {\n        newState.testStartTime = Date.now();\n      }\n      return newState;\n\n    // 新增：下一题（仅在QUESTIONS阶段使用）\n    case 'NEXT_QUESTION':\n      console.log('NEXT_QUESTION action triggered:', {\n        currentStage: state.currentStage,\n        currentQuestionIndex: state.currentQuestionIndex,\n        totalQuestions: state.questions.length,\n        answerVerified: state.answerVerified\n      });\n      if (state.currentStage !== 'QUESTIONS') {\n        console.log('Not in QUESTIONS stage, ignoring NEXT_QUESTION');\n        return state;\n      }\n\n      // 检查是否还有题目\n      if (state.currentQuestionIndex < state.questions.length - 1) {\n        // 下一题\n        console.log('Moving to next question:', state.currentQuestionIndex + 1);\n        return {\n          ...state,\n          currentQuestionIndex: state.currentQuestionIndex + 1,\n          answerVerified: false,\n          showingAnswer: false,\n          userSelection: {}\n        };\n      } else {\n        // 所有题目完成，进入结果页面\n        console.log('All questions completed, moving to RESULT stage');\n        return {\n          ...state,\n          currentStage: 'RESULT',\n          testEndTime: Date.now() // 记录测试结束时间\n        };\n      }\n    case 'PREV_STAGE':\n      const prevStages = Object.values(LEARNING_STAGES);\n      const prevIndex = prevStages.indexOf(state.currentStage);\n      if (state.currentStage === 'QUESTIONS' && state.currentQuestionIndex > 0) {\n        // 在练习阶段，返回上一题\n        return {\n          ...state,\n          currentQuestionIndex: state.currentQuestionIndex - 1,\n          answerVerified: false,\n          showingAnswer: false,\n          userSelection: {}\n        };\n      } else {\n        // 返回上一阶段\n        const prevStage = prevStages[prevIndex - 1];\n        return {\n          ...state,\n          currentStage: prevStage || state.currentStage,\n          currentQuestionIndex: 0\n        };\n      }\n    case 'SAVE_USER_ANSWER':\n      return {\n        ...state,\n        userAnswers: {\n          ...state.userAnswers,\n          [action.payload.questionId]: action.payload.answer\n        }\n      };\n    case 'SET_FINAL_RESULT':\n      return {\n        ...state,\n        finalResult: action.payload\n      };\n    case 'RESET_LEARNING':\n      // 重置学习状态，但保留知识点和题目数据\n      // 根据知识点的内容决定初始阶段\n      const hasQuestions = state.questions && state.questions.length > 0;\n      const hasVideo = ((_state$knowledgePoint5 = state.knowledgePoint) === null || _state$knowledgePoint5 === void 0 ? void 0 : _state$knowledgePoint5.videoCollectionId) && state.knowledgePoint.videoCollectionId !== null && state.knowledgePoint.videoCollectionId !== '';\n      console.log('重置学习状态:', {\n        knowledgePointId: (_state$knowledgePoint6 = state.knowledgePoint) === null || _state$knowledgePoint6 === void 0 ? void 0 : _state$knowledgePoint6.id,\n        knowledgePointName: (_state$knowledgePoint7 = state.knowledgePoint) === null || _state$knowledgePoint7 === void 0 ? void 0 : _state$knowledgePoint7.name,\n        hasVideo: hasVideo,\n        hasQuestions: hasQuestions,\n        videoCollectionId: (_state$knowledgePoint8 = state.knowledgePoint) === null || _state$knowledgePoint8 === void 0 ? void 0 : _state$knowledgePoint8.videoCollectionId,\n        questionsCount: ((_state$questions2 = state.questions) === null || _state$questions2 === void 0 ? void 0 : _state$questions2.length) || 0\n      });\n\n      // 决定重置后的初始阶段\n      let resetStage = 'INTRO';\n      if (hasQuestions && !hasVideo) {\n        // 有题目但没有视频，直接跳到题目阶段\n        resetStage = 'QUESTIONS';\n        console.log('重置到QUESTIONS阶段（有题目但没有视频）');\n      } else if (!hasQuestions && !hasVideo) {\n        // 既没有视频也没有题目，直接跳到结果阶段\n        resetStage = 'RESULT';\n        console.log('重置到RESULT阶段（既没有视频也没有题目）');\n      } else {\n        console.log('重置到INTRO阶段（正常流程）');\n      }\n      const resetState = {\n        ...initialState,\n        knowledgePoint: state.knowledgePoint,\n        questions: state.questions,\n        currentStage: resetStage\n      };\n\n      // 如果重置后直接进入QUESTIONS阶段，记录开始时间\n      if (resetStage === 'QUESTIONS') {\n        resetState.testStartTime = Date.now();\n      }\n      return resetState;\n\n    // 新增：使用新题目重新开始练习\n    case 'RESTART_WITH_NEW_QUESTIONS':\n      const {\n        knowledgePoint: newKnowledgePoint,\n        questions: newQuestions\n      } = action.payload;\n      console.log('使用新题目重新开始练习:', {\n        knowledgePointId: newKnowledgePoint === null || newKnowledgePoint === void 0 ? void 0 : newKnowledgePoint.id,\n        knowledgePointName: newKnowledgePoint === null || newKnowledgePoint === void 0 ? void 0 : newKnowledgePoint.name,\n        newQuestionsCount: (newQuestions === null || newQuestions === void 0 ? void 0 : newQuestions.length) || 0\n      });\n\n      // 根据知识点的内容决定初始阶段\n      const hasNewQuestions = newQuestions && newQuestions.length > 0;\n      const hasNewVideo = (newKnowledgePoint === null || newKnowledgePoint === void 0 ? void 0 : newKnowledgePoint.videoCollectionId) && newKnowledgePoint.videoCollectionId !== null && newKnowledgePoint.videoCollectionId !== '';\n\n      // 决定重新开始后的初始阶段\n      let restartStage = 'INTRO';\n      if (hasNewQuestions && !hasNewVideo) {\n        // 有题目但没有视频，直接跳到题目阶段\n        restartStage = 'QUESTIONS';\n        console.log('重新开始到QUESTIONS阶段（有题目但没有视频）');\n      } else if (!hasNewQuestions && !hasNewVideo) {\n        // 既没有视频也没有题目，直接跳到结果阶段\n        restartStage = 'RESULT';\n        console.log('重新开始到RESULT阶段（既没有视频也没有题目）');\n      } else {\n        console.log('重新开始到INTRO阶段（正常流程）');\n      }\n      const restartState = {\n        ...initialState,\n        knowledgePoint: newKnowledgePoint,\n        questions: newQuestions,\n        currentStage: restartStage,\n        loading: false,\n        // 确保加载状态结束\n        error: null\n      };\n\n      // 如果重新开始后直接进入QUESTIONS阶段，记录开始时间\n      if (restartStage === 'QUESTIONS') {\n        restartState.testStartTime = Date.now();\n      }\n      return restartState;\n\n    // 新增：设置用户当前选择 - 优化避免不必要的更新\n    case 'SET_USER_SELECTION':\n      // 获取当前问题ID的选择\n      const questionId = action.payload.questionId;\n      const currentSelection = state.userSelection[questionId] || {};\n      const newSelection = action.payload.selection;\n\n      // 如果是填空题（即答案是对象），则合并所有空格的答案\n      const combinedSelection = typeof newSelection === 'object' && newSelection !== null && !Array.isArray(newSelection) ? {\n        ...currentSelection,\n        ...newSelection\n      } : newSelection; // 非填空题直接使用新选择\n\n      // 检查是否真的有变化，避免不必要的更新\n      const hasChanged = typeof combinedSelection === 'object' && combinedSelection !== null ? JSON.stringify(currentSelection) !== JSON.stringify(combinedSelection) : currentSelection !== combinedSelection;\n      if (!hasChanged) {\n        return state; // 没有变化，不更新状态\n      }\n      return {\n        ...state,\n        userSelection: {\n          ...state.userSelection,\n          [questionId]: combinedSelection\n        }\n      };\n\n    // 新增：验证答案\n    case 'VERIFY_ANSWER':\n      {\n        const verifyQuestionId = action.payload.questionId;\n        const verificationResult = action.payload.verificationResult;\n\n        // 更新题目数据，添加subResults（如果存在）\n        const updatedQuestions = state.questions.map(question => {\n          if (question.id === verifyQuestionId && verificationResult !== null && verificationResult !== void 0 && verificationResult.subResults) {\n            return {\n              ...question,\n              subResults: verificationResult.subResults\n            };\n          }\n          return question;\n        });\n        return {\n          ...state,\n          answerVerified: true,\n          questions: updatedQuestions,\n          // 保存用户答案\n          userAnswers: {\n            ...state.userAnswers,\n            [verifyQuestionId]: {\n              // 安全处理answer为null的情况（跳过题目时）\n              ...(action.payload.answer || {}),\n              isCorrect: (verificationResult === null || verificationResult === void 0 ? void 0 : verificationResult.isCorrect) || false,\n              isSkipped: (verificationResult === null || verificationResult === void 0 ? void 0 : verificationResult.isSkipped) || false,\n              feedback: verificationResult\n            }\n          }\n        };\n      }\n\n    // 新增：显示答案和解析\n    case 'SHOW_ANSWER':\n      return {\n        ...state,\n        showingAnswer: true\n      };\n\n    // 新增：重置答案状态\n    case 'RESET_ANSWER_STATE':\n      return {\n        ...state,\n        answerVerified: false,\n        showingAnswer: false,\n        userSelection: {}\n      };\n\n    // 新增：进入查看题目模式\n    case 'ENTER_REVIEW_MODE':\n      return {\n        ...state,\n        currentStage: 'REVIEW',\n        currentQuestionIndex: 0,\n        answerVerified: true,\n        // 在查看模式下显示答案\n        showingAnswer: true\n      };\n\n    // 新增：查看模式下的题目导航\n    case 'PREV_QUESTION':\n      return {\n        ...state,\n        currentQuestionIndex: Math.max(0, state.currentQuestionIndex - 1)\n      };\n    case 'NEXT_QUESTION':\n      return {\n        ...state,\n        currentQuestionIndex: Math.min(state.questions.length - 1, state.currentQuestionIndex + 1)\n      };\n    case 'GO_TO_RESULT':\n      return {\n        ...state,\n        currentStage: 'RESULT'\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["LEARNING_STAGES", "INTRO", "VIDEO", "QUESTIONS", "RESULT", "REVIEW", "initialState", "knowledgePoint", "questions", "currentStage", "currentQuestionIndex", "userAnswers", "userSelection", "answerVerified", "showingAnswer", "finalResult", "testStartTime", "testEndTime", "loading", "error", "learningReducer", "state", "action", "_state$knowledgePoint5", "_state$knowledgePoint6", "_state$knowledgePoint7", "_state$knowledgePoint8", "_state$questions2", "type", "payload", "console", "log", "hasQuestions", "Array", "isArray", "stages", "Object", "values", "currentIndex", "indexOf", "nextStage", "_state$knowledgePoint", "_state$knowledgePoint2", "_state$knowledgePoint3", "_state$knowledgePoint4", "_state$questions", "hasVideo", "videoCollectionId", "knowledgePointId", "id", "knowledgePointName", "name", "questionsCount", "length", "newState", "Date", "now", "totalQuestions", "prevStages", "prevIndex", "prevStage", "questionId", "answer", "resetStage", "resetState", "newKnowledgePoint", "newQuestions", "newQuestionsCount", "hasNewQuestions", "hasNewVideo", "restartStage", "restartState", "currentSelection", "newSelection", "selection", "combinedSelection", "has<PERSON><PERSON>ed", "JSON", "stringify", "verifyQuestionId", "verificationResult", "updatedQuestions", "map", "question", "subResults", "isCorrect", "isSkipped", "feedback", "Math", "max", "min"], "sources": ["D:/projects/AIstrusys/frontend/learning/src/components/LearningModule/reducer.js"], "sourcesContent": ["/**\r\n * 学习模块状态管理Reducer\r\n */\r\n\r\n// 学习阶段常量\r\nexport const LEARNING_STAGES = {\r\n  INTRO: 'INTRO',\r\n  VIDEO: 'VIDEO',\r\n  QUESTIONS: 'QUESTIONS',\r\n  RESULT: 'RESULT',\r\n  REVIEW: 'REVIEW'  // 新增：查看题目阶段\r\n};\r\n\r\n// 初始状态\r\nexport const initialState = {\r\n  knowledgePoint: null,\r\n  questions: [],                    // 题目列表\r\n  currentStage: 'INTRO',           // 当前学习阶段\r\n  currentQuestionIndex: 0,         // 当前题目索引\r\n  userAnswers: {},\r\n  userSelection: {},               // 用户当前选择但尚未提交的答案\r\n  answerVerified: false,           // 标记当前题目是否已验证答案\r\n  showingAnswer: false,            // 标记是否正在显示答案和解析\r\n  finalResult: null,\r\n  testStartTime: null,             // 测试开始时间\r\n  testEndTime: null,               // 测试结束时间\r\n  loading: false,\r\n  error: null\r\n};\r\n\r\n/**\r\n * 学习模块Reducer\r\n * \r\n * @param {Object} state 当前状态\r\n * @param {Object} action 动作对象\r\n * @returns {Object} 新状态\r\n */\r\nexport const learningReducer = (state, action) => {\r\n  switch (action.type) {\r\n    case 'FETCH_START':\r\n      return {\r\n        ...state,\r\n        loading: true,\r\n        error: null\r\n      };\r\n      \r\n    case 'FETCH_SUCCESS':\r\n      // 确保数据结构正确，安全处理undefined payload\r\n      const payload = action.payload || {};\r\n      const knowledgePoint = payload.knowledgePoint || payload;\r\n      const questions = payload.questions;\r\n\r\n      console.log('Reducer处理的数据:', { knowledgePoint, questions, hasQuestions: !!questions });\r\n\r\n      // 如果payload为空对象，只更新loading状态，不重置数据\r\n      if (!knowledgePoint && !questions) {\r\n        console.log('Reducer - 空payload，仅更新loading状态');\r\n        return {\r\n          ...state,\r\n          loading: false,\r\n          error: null\r\n        };\r\n      }\r\n\r\n      return {\r\n        ...state,\r\n        loading: false,\r\n        error: null,\r\n        knowledgePoint: knowledgePoint || state.knowledgePoint,\r\n        questions: questions ? (Array.isArray(questions) ? questions : []) : state.questions,\r\n        currentStage: knowledgePoint ? 'INTRO' : state.currentStage\r\n      };\r\n      \r\n    case 'FETCH_ERROR':\r\n      return {\r\n        ...state,\r\n        loading: false,\r\n        error: action.payload\r\n      };\r\n\r\n    case 'FETCH_END':\r\n      // 仅结束加载状态，不重置数据\r\n      return {\r\n        ...state,\r\n        loading: false,\r\n        error: null\r\n      };\r\n\r\n    case 'RESET_ERROR':\r\n      return {\r\n        ...state,\r\n        error: null\r\n      };\r\n      \r\n    case 'NEXT_STAGE':\r\n      const stages = Object.values(LEARNING_STAGES);\r\n      const currentIndex = stages.indexOf(state.currentStage);\r\n\r\n      // 其他阶段进入下一阶段\r\n      let nextStage = stages[currentIndex + 1];\r\n\r\n      // 如果从INTRO阶段进入下一阶段，检查是否有视频内容\r\n      if (state.currentStage === 'INTRO' && nextStage === 'VIDEO') {\r\n        // 检查知识点是否有视频内容（videoCollectionId为空或null表示没有视频）\r\n        const hasVideo = state.knowledgePoint?.videoCollectionId &&\r\n                         state.knowledgePoint.videoCollectionId !== null &&\r\n                         state.knowledgePoint.videoCollectionId !== '';\r\n\r\n        console.log('检查视频内容:', {\r\n          knowledgePointId: state.knowledgePoint?.id,\r\n          knowledgePointName: state.knowledgePoint?.name,\r\n          videoCollectionId: state.knowledgePoint?.videoCollectionId,\r\n          hasVideo: hasVideo,\r\n          questionsCount: state.questions?.length || 0\r\n        });\r\n\r\n        if (!hasVideo) {\r\n          console.log('知识点没有视频内容，跳过VIDEO阶段');\r\n          // 跳过VIDEO阶段，直接进入QUESTIONS阶段\r\n          nextStage = 'QUESTIONS';\r\n\r\n          // 如果也没有题目，则直接跳到RESULT阶段\r\n          if (!state.questions || state.questions.length === 0) {\r\n            console.log('也没有题目，直接跳到RESULT阶段');\r\n            nextStage = 'RESULT';\r\n          }\r\n        } else {\r\n          console.log('知识点有视频内容，正常进入VIDEO阶段');\r\n        }\r\n      }\r\n\r\n      // 如果下一阶段是QUESTIONS但没有题目，跳过到RESULT\r\n      if (nextStage === 'QUESTIONS' && (!state.questions || state.questions.length === 0)) {\r\n        nextStage = 'RESULT';\r\n      }\r\n\r\n      // 如果进入QUESTIONS阶段，记录开始时间\r\n      const newState = {\r\n        ...state,\r\n        currentStage: nextStage || state.currentStage\r\n      };\r\n\r\n      if (nextStage === 'QUESTIONS' && !state.testStartTime) {\r\n        newState.testStartTime = Date.now();\r\n      }\r\n\r\n      return newState;\r\n\r\n    // 新增：下一题（仅在QUESTIONS阶段使用）\r\n    case 'NEXT_QUESTION':\r\n      console.log('NEXT_QUESTION action triggered:', {\r\n        currentStage: state.currentStage,\r\n        currentQuestionIndex: state.currentQuestionIndex,\r\n        totalQuestions: state.questions.length,\r\n        answerVerified: state.answerVerified\r\n      });\r\n\r\n      if (state.currentStage !== 'QUESTIONS') {\r\n        console.log('Not in QUESTIONS stage, ignoring NEXT_QUESTION');\r\n        return state;\r\n      }\r\n\r\n      // 检查是否还有题目\r\n      if (state.currentQuestionIndex < state.questions.length - 1) {\r\n        // 下一题\r\n        console.log('Moving to next question:', state.currentQuestionIndex + 1);\r\n        return {\r\n          ...state,\r\n          currentQuestionIndex: state.currentQuestionIndex + 1,\r\n          answerVerified: false,\r\n          showingAnswer: false,\r\n          userSelection: {}\r\n        };\r\n      } else {\r\n        // 所有题目完成，进入结果页面\r\n        console.log('All questions completed, moving to RESULT stage');\r\n        return {\r\n          ...state,\r\n          currentStage: 'RESULT',\r\n          testEndTime: Date.now() // 记录测试结束时间\r\n        };\r\n      }\r\n\r\n    case 'PREV_STAGE':\r\n      const prevStages = Object.values(LEARNING_STAGES);\r\n      const prevIndex = prevStages.indexOf(state.currentStage);\r\n\r\n      if (state.currentStage === 'QUESTIONS' && state.currentQuestionIndex > 0) {\r\n        // 在练习阶段，返回上一题\r\n        return {\r\n          ...state,\r\n          currentQuestionIndex: state.currentQuestionIndex - 1,\r\n          answerVerified: false,\r\n          showingAnswer: false,\r\n          userSelection: {}\r\n        };\r\n      } else {\r\n        // 返回上一阶段\r\n        const prevStage = prevStages[prevIndex - 1];\r\n        return {\r\n          ...state,\r\n          currentStage: prevStage || state.currentStage,\r\n          currentQuestionIndex: 0\r\n        };\r\n      }\r\n      \r\n    case 'SAVE_USER_ANSWER':\r\n      return {\r\n        ...state,\r\n        userAnswers: {\r\n          ...state.userAnswers,\r\n          [action.payload.questionId]: action.payload.answer\r\n        }\r\n      };\r\n      \r\n    case 'SET_FINAL_RESULT':\r\n      return {\r\n        ...state,\r\n        finalResult: action.payload\r\n      };\r\n      \r\n    case 'RESET_LEARNING':\r\n      // 重置学习状态，但保留知识点和题目数据\r\n      // 根据知识点的内容决定初始阶段\r\n      const hasQuestions = state.questions && state.questions.length > 0;\r\n      const hasVideo = state.knowledgePoint?.videoCollectionId &&\r\n                       state.knowledgePoint.videoCollectionId !== null &&\r\n                       state.knowledgePoint.videoCollectionId !== '';\r\n\r\n      console.log('重置学习状态:', {\r\n        knowledgePointId: state.knowledgePoint?.id,\r\n        knowledgePointName: state.knowledgePoint?.name,\r\n        hasVideo: hasVideo,\r\n        hasQuestions: hasQuestions,\r\n        videoCollectionId: state.knowledgePoint?.videoCollectionId,\r\n        questionsCount: state.questions?.length || 0\r\n      });\r\n\r\n      // 决定重置后的初始阶段\r\n      let resetStage = 'INTRO';\r\n      if (hasQuestions && !hasVideo) {\r\n        // 有题目但没有视频，直接跳到题目阶段\r\n        resetStage = 'QUESTIONS';\r\n        console.log('重置到QUESTIONS阶段（有题目但没有视频）');\r\n      } else if (!hasQuestions && !hasVideo) {\r\n        // 既没有视频也没有题目，直接跳到结果阶段\r\n        resetStage = 'RESULT';\r\n        console.log('重置到RESULT阶段（既没有视频也没有题目）');\r\n      } else {\r\n        console.log('重置到INTRO阶段（正常流程）');\r\n      }\r\n\r\n      const resetState = {\r\n        ...initialState,\r\n        knowledgePoint: state.knowledgePoint,\r\n        questions: state.questions,\r\n        currentStage: resetStage\r\n      };\r\n\r\n      // 如果重置后直接进入QUESTIONS阶段，记录开始时间\r\n      if (resetStage === 'QUESTIONS') {\r\n        resetState.testStartTime = Date.now();\r\n      }\r\n\r\n      return resetState;\r\n\r\n    // 新增：使用新题目重新开始练习\r\n    case 'RESTART_WITH_NEW_QUESTIONS':\r\n      const { knowledgePoint: newKnowledgePoint, questions: newQuestions } = action.payload;\r\n\r\n      console.log('使用新题目重新开始练习:', {\r\n        knowledgePointId: newKnowledgePoint?.id,\r\n        knowledgePointName: newKnowledgePoint?.name,\r\n        newQuestionsCount: newQuestions?.length || 0\r\n      });\r\n\r\n      // 根据知识点的内容决定初始阶段\r\n      const hasNewQuestions = newQuestions && newQuestions.length > 0;\r\n      const hasNewVideo = newKnowledgePoint?.videoCollectionId &&\r\n                         newKnowledgePoint.videoCollectionId !== null &&\r\n                         newKnowledgePoint.videoCollectionId !== '';\r\n\r\n      // 决定重新开始后的初始阶段\r\n      let restartStage = 'INTRO';\r\n      if (hasNewQuestions && !hasNewVideo) {\r\n        // 有题目但没有视频，直接跳到题目阶段\r\n        restartStage = 'QUESTIONS';\r\n        console.log('重新开始到QUESTIONS阶段（有题目但没有视频）');\r\n      } else if (!hasNewQuestions && !hasNewVideo) {\r\n        // 既没有视频也没有题目，直接跳到结果阶段\r\n        restartStage = 'RESULT';\r\n        console.log('重新开始到RESULT阶段（既没有视频也没有题目）');\r\n      } else {\r\n        console.log('重新开始到INTRO阶段（正常流程）');\r\n      }\r\n\r\n      const restartState = {\r\n        ...initialState,\r\n        knowledgePoint: newKnowledgePoint,\r\n        questions: newQuestions,\r\n        currentStage: restartStage,\r\n        loading: false, // 确保加载状态结束\r\n        error: null\r\n      };\r\n\r\n      // 如果重新开始后直接进入QUESTIONS阶段，记录开始时间\r\n      if (restartStage === 'QUESTIONS') {\r\n        restartState.testStartTime = Date.now();\r\n      }\r\n\r\n      return restartState;\r\n\r\n    // 新增：设置用户当前选择 - 优化避免不必要的更新\r\n    case 'SET_USER_SELECTION':\r\n      // 获取当前问题ID的选择\r\n      const questionId = action.payload.questionId;\r\n      const currentSelection = state.userSelection[questionId] || {};\r\n      const newSelection = action.payload.selection;\r\n\r\n      // 如果是填空题（即答案是对象），则合并所有空格的答案\r\n      const combinedSelection =\r\n        typeof newSelection === 'object' && newSelection !== null && !Array.isArray(newSelection)\r\n          ? { ...currentSelection, ...newSelection }\r\n          : newSelection; // 非填空题直接使用新选择\r\n\r\n      // 检查是否真的有变化，避免不必要的更新\r\n      const hasChanged = typeof combinedSelection === 'object' && combinedSelection !== null\r\n        ? JSON.stringify(currentSelection) !== JSON.stringify(combinedSelection)\r\n        : currentSelection !== combinedSelection;\r\n\r\n      if (!hasChanged) {\r\n        return state; // 没有变化，不更新状态\r\n      }\r\n\r\n      return {\r\n        ...state,\r\n        userSelection: {\r\n          ...state.userSelection,\r\n          [questionId]: combinedSelection\r\n        }\r\n      };\r\n      \r\n    // 新增：验证答案\r\n    case 'VERIFY_ANSWER': {\r\n      const verifyQuestionId = action.payload.questionId;\r\n      const verificationResult = action.payload.verificationResult;\r\n\r\n      // 更新题目数据，添加subResults（如果存在）\r\n      const updatedQuestions = state.questions.map(question => {\r\n        if (question.id === verifyQuestionId && verificationResult?.subResults) {\r\n          return {\r\n            ...question,\r\n            subResults: verificationResult.subResults\r\n          };\r\n        }\r\n        return question;\r\n      });\r\n\r\n      return {\r\n        ...state,\r\n        answerVerified: true,\r\n        questions: updatedQuestions,\r\n        // 保存用户答案\r\n        userAnswers: {\r\n          ...state.userAnswers,\r\n          [verifyQuestionId]: {\r\n            // 安全处理answer为null的情况（跳过题目时）\r\n            ...(action.payload.answer || {}),\r\n            isCorrect: verificationResult?.isCorrect || false,\r\n            isSkipped: verificationResult?.isSkipped || false,\r\n            feedback: verificationResult\r\n          }\r\n        }\r\n      };\r\n    }\r\n      \r\n    // 新增：显示答案和解析\r\n    case 'SHOW_ANSWER':\r\n      return {\r\n        ...state,\r\n        showingAnswer: true\r\n      };\r\n\r\n    // 新增：重置答案状态\r\n    case 'RESET_ANSWER_STATE':\r\n      return {\r\n        ...state,\r\n        answerVerified: false,\r\n        showingAnswer: false,\r\n        userSelection: {}\r\n      };\r\n\r\n    // 新增：进入查看题目模式\r\n    case 'ENTER_REVIEW_MODE':\r\n      return {\r\n        ...state,\r\n        currentStage: 'REVIEW',\r\n        currentQuestionIndex: 0,\r\n        answerVerified: true,  // 在查看模式下显示答案\r\n        showingAnswer: true\r\n      };\r\n\r\n    // 新增：查看模式下的题目导航\r\n    case 'PREV_QUESTION':\r\n      return {\r\n        ...state,\r\n        currentQuestionIndex: Math.max(0, state.currentQuestionIndex - 1)\r\n      };\r\n\r\n    case 'NEXT_QUESTION':\r\n      return {\r\n        ...state,\r\n        currentQuestionIndex: Math.min(state.questions.length - 1, state.currentQuestionIndex + 1)\r\n      };\r\n\r\n    case 'GO_TO_RESULT':\r\n      return {\r\n        ...state,\r\n        currentStage: 'RESULT'\r\n      };\r\n\r\n    default:\r\n      return state;\r\n  }\r\n}; "], "mappings": "AAAA;AACA;AACA;;AAEA;AACA,OAAO,MAAMA,eAAe,GAAG;EAC7BC,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,OAAO;EACdC,SAAS,EAAE,WAAW;EACtBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ,CAAE;AACpB,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,cAAc,EAAE,IAAI;EACpBC,SAAS,EAAE,EAAE;EAAqB;EAClCC,YAAY,EAAE,OAAO;EAAY;EACjCC,oBAAoB,EAAE,CAAC;EAAU;EACjCC,WAAW,EAAE,CAAC,CAAC;EACfC,aAAa,EAAE,CAAC,CAAC;EAAgB;EACjCC,cAAc,EAAE,KAAK;EAAY;EACjCC,aAAa,EAAE,KAAK;EAAa;EACjCC,WAAW,EAAE,IAAI;EACjBC,aAAa,EAAE,IAAI;EAAc;EACjCC,WAAW,EAAE,IAAI;EAAgB;EACjCC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE;AACT,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,eAAe,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,iBAAA;EAChD,QAAQL,MAAM,CAACM,IAAI;IACjB,KAAK,aAAa;MAChB,OAAO;QACL,GAAGP,KAAK;QACRH,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;MACT,CAAC;IAEH,KAAK,eAAe;MAClB;MACA,MAAMU,OAAO,GAAGP,MAAM,CAACO,OAAO,IAAI,CAAC,CAAC;MACpC,MAAMtB,cAAc,GAAGsB,OAAO,CAACtB,cAAc,IAAIsB,OAAO;MACxD,MAAMrB,SAAS,GAAGqB,OAAO,CAACrB,SAAS;MAEnCsB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE;QAAExB,cAAc;QAAEC,SAAS;QAAEwB,YAAY,EAAE,CAAC,CAACxB;MAAU,CAAC,CAAC;;MAEtF;MACA,IAAI,CAACD,cAAc,IAAI,CAACC,SAAS,EAAE;QACjCsB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,OAAO;UACL,GAAGV,KAAK;UACRH,OAAO,EAAE,KAAK;UACdC,KAAK,EAAE;QACT,CAAC;MACH;MAEA,OAAO;QACL,GAAGE,KAAK;QACRH,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE,IAAI;QACXZ,cAAc,EAAEA,cAAc,IAAIc,KAAK,CAACd,cAAc;QACtDC,SAAS,EAAEA,SAAS,GAAIyB,KAAK,CAACC,OAAO,CAAC1B,SAAS,CAAC,GAAGA,SAAS,GAAG,EAAE,GAAIa,KAAK,CAACb,SAAS;QACpFC,YAAY,EAAEF,cAAc,GAAG,OAAO,GAAGc,KAAK,CAACZ;MACjD,CAAC;IAEH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGY,KAAK;QACRH,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEG,MAAM,CAACO;MAChB,CAAC;IAEH,KAAK,WAAW;MACd;MACA,OAAO;QACL,GAAGR,KAAK;QACRH,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC;IAEH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGE,KAAK;QACRF,KAAK,EAAE;MACT,CAAC;IAEH,KAAK,YAAY;MACf,MAAMgB,MAAM,GAAGC,MAAM,CAACC,MAAM,CAACrC,eAAe,CAAC;MAC7C,MAAMsC,YAAY,GAAGH,MAAM,CAACI,OAAO,CAAClB,KAAK,CAACZ,YAAY,CAAC;;MAEvD;MACA,IAAI+B,SAAS,GAAGL,MAAM,CAACG,YAAY,GAAG,CAAC,CAAC;;MAExC;MACA,IAAIjB,KAAK,CAACZ,YAAY,KAAK,OAAO,IAAI+B,SAAS,KAAK,OAAO,EAAE;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA;QAC3D;QACA,MAAMC,QAAQ,GAAG,EAAAL,qBAAA,GAAApB,KAAK,CAACd,cAAc,cAAAkC,qBAAA,uBAApBA,qBAAA,CAAsBM,iBAAiB,KACvC1B,KAAK,CAACd,cAAc,CAACwC,iBAAiB,KAAK,IAAI,IAC/C1B,KAAK,CAACd,cAAc,CAACwC,iBAAiB,KAAK,EAAE;QAE9DjB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;UACrBiB,gBAAgB,GAAAN,sBAAA,GAAErB,KAAK,CAACd,cAAc,cAAAmC,sBAAA,uBAApBA,sBAAA,CAAsBO,EAAE;UAC1CC,kBAAkB,GAAAP,sBAAA,GAAEtB,KAAK,CAACd,cAAc,cAAAoC,sBAAA,uBAApBA,sBAAA,CAAsBQ,IAAI;UAC9CJ,iBAAiB,GAAAH,sBAAA,GAAEvB,KAAK,CAACd,cAAc,cAAAqC,sBAAA,uBAApBA,sBAAA,CAAsBG,iBAAiB;UAC1DD,QAAQ,EAAEA,QAAQ;UAClBM,cAAc,EAAE,EAAAP,gBAAA,GAAAxB,KAAK,CAACb,SAAS,cAAAqC,gBAAA,uBAAfA,gBAAA,CAAiBQ,MAAM,KAAI;QAC7C,CAAC,CAAC;QAEF,IAAI,CAACP,QAAQ,EAAE;UACbhB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;UAClC;UACAS,SAAS,GAAG,WAAW;;UAEvB;UACA,IAAI,CAACnB,KAAK,CAACb,SAAS,IAAIa,KAAK,CAACb,SAAS,CAAC6C,MAAM,KAAK,CAAC,EAAE;YACpDvB,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;YACjCS,SAAS,GAAG,QAAQ;UACtB;QACF,CAAC,MAAM;UACLV,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;QACrC;MACF;;MAEA;MACA,IAAIS,SAAS,KAAK,WAAW,KAAK,CAACnB,KAAK,CAACb,SAAS,IAAIa,KAAK,CAACb,SAAS,CAAC6C,MAAM,KAAK,CAAC,CAAC,EAAE;QACnFb,SAAS,GAAG,QAAQ;MACtB;;MAEA;MACA,MAAMc,QAAQ,GAAG;QACf,GAAGjC,KAAK;QACRZ,YAAY,EAAE+B,SAAS,IAAInB,KAAK,CAACZ;MACnC,CAAC;MAED,IAAI+B,SAAS,KAAK,WAAW,IAAI,CAACnB,KAAK,CAACL,aAAa,EAAE;QACrDsC,QAAQ,CAACtC,aAAa,GAAGuC,IAAI,CAACC,GAAG,CAAC,CAAC;MACrC;MAEA,OAAOF,QAAQ;;IAEjB;IACA,KAAK,eAAe;MAClBxB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;QAC7CtB,YAAY,EAAEY,KAAK,CAACZ,YAAY;QAChCC,oBAAoB,EAAEW,KAAK,CAACX,oBAAoB;QAChD+C,cAAc,EAAEpC,KAAK,CAACb,SAAS,CAAC6C,MAAM;QACtCxC,cAAc,EAAEQ,KAAK,CAACR;MACxB,CAAC,CAAC;MAEF,IAAIQ,KAAK,CAACZ,YAAY,KAAK,WAAW,EAAE;QACtCqB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC7D,OAAOV,KAAK;MACd;;MAEA;MACA,IAAIA,KAAK,CAACX,oBAAoB,GAAGW,KAAK,CAACb,SAAS,CAAC6C,MAAM,GAAG,CAAC,EAAE;QAC3D;QACAvB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEV,KAAK,CAACX,oBAAoB,GAAG,CAAC,CAAC;QACvE,OAAO;UACL,GAAGW,KAAK;UACRX,oBAAoB,EAAEW,KAAK,CAACX,oBAAoB,GAAG,CAAC;UACpDG,cAAc,EAAE,KAAK;UACrBC,aAAa,EAAE,KAAK;UACpBF,aAAa,EAAE,CAAC;QAClB,CAAC;MACH,CAAC,MAAM;QACL;QACAkB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAC9D,OAAO;UACL,GAAGV,KAAK;UACRZ,YAAY,EAAE,QAAQ;UACtBQ,WAAW,EAAEsC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;QAC1B,CAAC;MACH;IAEF,KAAK,YAAY;MACf,MAAME,UAAU,GAAGtB,MAAM,CAACC,MAAM,CAACrC,eAAe,CAAC;MACjD,MAAM2D,SAAS,GAAGD,UAAU,CAACnB,OAAO,CAAClB,KAAK,CAACZ,YAAY,CAAC;MAExD,IAAIY,KAAK,CAACZ,YAAY,KAAK,WAAW,IAAIY,KAAK,CAACX,oBAAoB,GAAG,CAAC,EAAE;QACxE;QACA,OAAO;UACL,GAAGW,KAAK;UACRX,oBAAoB,EAAEW,KAAK,CAACX,oBAAoB,GAAG,CAAC;UACpDG,cAAc,EAAE,KAAK;UACrBC,aAAa,EAAE,KAAK;UACpBF,aAAa,EAAE,CAAC;QAClB,CAAC;MACH,CAAC,MAAM;QACL;QACA,MAAMgD,SAAS,GAAGF,UAAU,CAACC,SAAS,GAAG,CAAC,CAAC;QAC3C,OAAO;UACL,GAAGtC,KAAK;UACRZ,YAAY,EAAEmD,SAAS,IAAIvC,KAAK,CAACZ,YAAY;UAC7CC,oBAAoB,EAAE;QACxB,CAAC;MACH;IAEF,KAAK,kBAAkB;MACrB,OAAO;QACL,GAAGW,KAAK;QACRV,WAAW,EAAE;UACX,GAAGU,KAAK,CAACV,WAAW;UACpB,CAACW,MAAM,CAACO,OAAO,CAACgC,UAAU,GAAGvC,MAAM,CAACO,OAAO,CAACiC;QAC9C;MACF,CAAC;IAEH,KAAK,kBAAkB;MACrB,OAAO;QACL,GAAGzC,KAAK;QACRN,WAAW,EAAEO,MAAM,CAACO;MACtB,CAAC;IAEH,KAAK,gBAAgB;MACnB;MACA;MACA,MAAMG,YAAY,GAAGX,KAAK,CAACb,SAAS,IAAIa,KAAK,CAACb,SAAS,CAAC6C,MAAM,GAAG,CAAC;MAClE,MAAMP,QAAQ,GAAG,EAAAvB,sBAAA,GAAAF,KAAK,CAACd,cAAc,cAAAgB,sBAAA,uBAApBA,sBAAA,CAAsBwB,iBAAiB,KACvC1B,KAAK,CAACd,cAAc,CAACwC,iBAAiB,KAAK,IAAI,IAC/C1B,KAAK,CAACd,cAAc,CAACwC,iBAAiB,KAAK,EAAE;MAE9DjB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBiB,gBAAgB,GAAAxB,sBAAA,GAAEH,KAAK,CAACd,cAAc,cAAAiB,sBAAA,uBAApBA,sBAAA,CAAsByB,EAAE;QAC1CC,kBAAkB,GAAAzB,sBAAA,GAAEJ,KAAK,CAACd,cAAc,cAAAkB,sBAAA,uBAApBA,sBAAA,CAAsB0B,IAAI;QAC9CL,QAAQ,EAAEA,QAAQ;QAClBd,YAAY,EAAEA,YAAY;QAC1Be,iBAAiB,GAAArB,sBAAA,GAAEL,KAAK,CAACd,cAAc,cAAAmB,sBAAA,uBAApBA,sBAAA,CAAsBqB,iBAAiB;QAC1DK,cAAc,EAAE,EAAAzB,iBAAA,GAAAN,KAAK,CAACb,SAAS,cAAAmB,iBAAA,uBAAfA,iBAAA,CAAiB0B,MAAM,KAAI;MAC7C,CAAC,CAAC;;MAEF;MACA,IAAIU,UAAU,GAAG,OAAO;MACxB,IAAI/B,YAAY,IAAI,CAACc,QAAQ,EAAE;QAC7B;QACAiB,UAAU,GAAG,WAAW;QACxBjC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACzC,CAAC,MAAM,IAAI,CAACC,YAAY,IAAI,CAACc,QAAQ,EAAE;QACrC;QACAiB,UAAU,GAAG,QAAQ;QACrBjC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACxC,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MACjC;MAEA,MAAMiC,UAAU,GAAG;QACjB,GAAG1D,YAAY;QACfC,cAAc,EAAEc,KAAK,CAACd,cAAc;QACpCC,SAAS,EAAEa,KAAK,CAACb,SAAS;QAC1BC,YAAY,EAAEsD;MAChB,CAAC;;MAED;MACA,IAAIA,UAAU,KAAK,WAAW,EAAE;QAC9BC,UAAU,CAAChD,aAAa,GAAGuC,IAAI,CAACC,GAAG,CAAC,CAAC;MACvC;MAEA,OAAOQ,UAAU;;IAEnB;IACA,KAAK,4BAA4B;MAC/B,MAAM;QAAEzD,cAAc,EAAE0D,iBAAiB;QAAEzD,SAAS,EAAE0D;MAAa,CAAC,GAAG5C,MAAM,CAACO,OAAO;MAErFC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;QAC1BiB,gBAAgB,EAAEiB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEhB,EAAE;QACvCC,kBAAkB,EAAEe,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEd,IAAI;QAC3CgB,iBAAiB,EAAE,CAAAD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEb,MAAM,KAAI;MAC7C,CAAC,CAAC;;MAEF;MACA,MAAMe,eAAe,GAAGF,YAAY,IAAIA,YAAY,CAACb,MAAM,GAAG,CAAC;MAC/D,MAAMgB,WAAW,GAAG,CAAAJ,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAElB,iBAAiB,KACrCkB,iBAAiB,CAAClB,iBAAiB,KAAK,IAAI,IAC5CkB,iBAAiB,CAAClB,iBAAiB,KAAK,EAAE;;MAE7D;MACA,IAAIuB,YAAY,GAAG,OAAO;MAC1B,IAAIF,eAAe,IAAI,CAACC,WAAW,EAAE;QACnC;QACAC,YAAY,GAAG,WAAW;QAC1BxC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MAC3C,CAAC,MAAM,IAAI,CAACqC,eAAe,IAAI,CAACC,WAAW,EAAE;QAC3C;QACAC,YAAY,GAAG,QAAQ;QACvBxC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MAC1C,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACnC;MAEA,MAAMwC,YAAY,GAAG;QACnB,GAAGjE,YAAY;QACfC,cAAc,EAAE0D,iBAAiB;QACjCzD,SAAS,EAAE0D,YAAY;QACvBzD,YAAY,EAAE6D,YAAY;QAC1BpD,OAAO,EAAE,KAAK;QAAE;QAChBC,KAAK,EAAE;MACT,CAAC;;MAED;MACA,IAAImD,YAAY,KAAK,WAAW,EAAE;QAChCC,YAAY,CAACvD,aAAa,GAAGuC,IAAI,CAACC,GAAG,CAAC,CAAC;MACzC;MAEA,OAAOe,YAAY;;IAErB;IACA,KAAK,oBAAoB;MACvB;MACA,MAAMV,UAAU,GAAGvC,MAAM,CAACO,OAAO,CAACgC,UAAU;MAC5C,MAAMW,gBAAgB,GAAGnD,KAAK,CAACT,aAAa,CAACiD,UAAU,CAAC,IAAI,CAAC,CAAC;MAC9D,MAAMY,YAAY,GAAGnD,MAAM,CAACO,OAAO,CAAC6C,SAAS;;MAE7C;MACA,MAAMC,iBAAiB,GACrB,OAAOF,YAAY,KAAK,QAAQ,IAAIA,YAAY,KAAK,IAAI,IAAI,CAACxC,KAAK,CAACC,OAAO,CAACuC,YAAY,CAAC,GACrF;QAAE,GAAGD,gBAAgB;QAAE,GAAGC;MAAa,CAAC,GACxCA,YAAY,CAAC,CAAC;;MAEpB;MACA,MAAMG,UAAU,GAAG,OAAOD,iBAAiB,KAAK,QAAQ,IAAIA,iBAAiB,KAAK,IAAI,GAClFE,IAAI,CAACC,SAAS,CAACN,gBAAgB,CAAC,KAAKK,IAAI,CAACC,SAAS,CAACH,iBAAiB,CAAC,GACtEH,gBAAgB,KAAKG,iBAAiB;MAE1C,IAAI,CAACC,UAAU,EAAE;QACf,OAAOvD,KAAK,CAAC,CAAC;MAChB;MAEA,OAAO;QACL,GAAGA,KAAK;QACRT,aAAa,EAAE;UACb,GAAGS,KAAK,CAACT,aAAa;UACtB,CAACiD,UAAU,GAAGc;QAChB;MACF,CAAC;;IAEH;IACA,KAAK,eAAe;MAAE;QACpB,MAAMI,gBAAgB,GAAGzD,MAAM,CAACO,OAAO,CAACgC,UAAU;QAClD,MAAMmB,kBAAkB,GAAG1D,MAAM,CAACO,OAAO,CAACmD,kBAAkB;;QAE5D;QACA,MAAMC,gBAAgB,GAAG5D,KAAK,CAACb,SAAS,CAAC0E,GAAG,CAACC,QAAQ,IAAI;UACvD,IAAIA,QAAQ,CAAClC,EAAE,KAAK8B,gBAAgB,IAAIC,kBAAkB,aAAlBA,kBAAkB,eAAlBA,kBAAkB,CAAEI,UAAU,EAAE;YACtE,OAAO;cACL,GAAGD,QAAQ;cACXC,UAAU,EAAEJ,kBAAkB,CAACI;YACjC,CAAC;UACH;UACA,OAAOD,QAAQ;QACjB,CAAC,CAAC;QAEF,OAAO;UACL,GAAG9D,KAAK;UACRR,cAAc,EAAE,IAAI;UACpBL,SAAS,EAAEyE,gBAAgB;UAC3B;UACAtE,WAAW,EAAE;YACX,GAAGU,KAAK,CAACV,WAAW;YACpB,CAACoE,gBAAgB,GAAG;cAClB;cACA,IAAIzD,MAAM,CAACO,OAAO,CAACiC,MAAM,IAAI,CAAC,CAAC,CAAC;cAChCuB,SAAS,EAAE,CAAAL,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEK,SAAS,KAAI,KAAK;cACjDC,SAAS,EAAE,CAAAN,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEM,SAAS,KAAI,KAAK;cACjDC,QAAQ,EAAEP;YACZ;UACF;QACF,CAAC;MACH;;IAEA;IACA,KAAK,aAAa;MAChB,OAAO;QACL,GAAG3D,KAAK;QACRP,aAAa,EAAE;MACjB,CAAC;;IAEH;IACA,KAAK,oBAAoB;MACvB,OAAO;QACL,GAAGO,KAAK;QACRR,cAAc,EAAE,KAAK;QACrBC,aAAa,EAAE,KAAK;QACpBF,aAAa,EAAE,CAAC;MAClB,CAAC;;IAEH;IACA,KAAK,mBAAmB;MACtB,OAAO;QACL,GAAGS,KAAK;QACRZ,YAAY,EAAE,QAAQ;QACtBC,oBAAoB,EAAE,CAAC;QACvBG,cAAc,EAAE,IAAI;QAAG;QACvBC,aAAa,EAAE;MACjB,CAAC;;IAEH;IACA,KAAK,eAAe;MAClB,OAAO;QACL,GAAGO,KAAK;QACRX,oBAAoB,EAAE8E,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEpE,KAAK,CAACX,oBAAoB,GAAG,CAAC;MAClE,CAAC;IAEH,KAAK,eAAe;MAClB,OAAO;QACL,GAAGW,KAAK;QACRX,oBAAoB,EAAE8E,IAAI,CAACE,GAAG,CAACrE,KAAK,CAACb,SAAS,CAAC6C,MAAM,GAAG,CAAC,EAAEhC,KAAK,CAACX,oBAAoB,GAAG,CAAC;MAC3F,CAAC;IAEH,KAAK,cAAc;MACjB,OAAO;QACL,GAAGW,KAAK;QACRZ,YAAY,EAAE;MAChB,CAAC;IAEH;MACE,OAAOY,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}