package com.example.backend.service.impl;

import com.example.backend.model.*;
import com.example.backend.repository.*;
import com.example.backend.service.QuestionSelectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 题目选择服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class QuestionSelectionServiceImpl implements QuestionSelectionService {

    private final QuestionRepository questionRepository;
    private final KnowledgePointRepository knowledgePointRepository;
    private final StudentAnswerRepository studentAnswerRepository;
    private final PracticeConfigRepository practiceConfigRepository;

    // 默认配置常量
    private static final Integer DEFAULT_KNOWLEDGE_POINT_PRACTICE_COUNT = 5;
    private static final Integer DEFAULT_CHAPTER_TEST_DIVISOR = 10;
    private static final Integer MIN_CHAPTER_TEST_QUESTIONS = 1;
    private static final Integer MAX_CHAPTER_TEST_TOTAL = 50;

    @Override
    @Transactional(readOnly = true)
    public List<Question> selectQuestionsForKnowledgePointPractice(Long knowledgePointId, Long studentId) {
        log.info("开始为知识点 {} 选择练习题目，学生ID: {}", knowledgePointId, studentId);

        try {
            // 参数验证
            if (knowledgePointId == null) {
                log.error("知识点ID不能为空");
                throw new IllegalArgumentException("知识点ID不能为空");
            }

            // 获取管理端配置的基础题目数量
            Integer baseQuestionCount = getEffectivePracticeCount(knowledgePointId);
            log.debug("知识点 {} 的管理端配置基础题目数量: {}", knowledgePointId, baseQuestionCount);

            // 在基础数量上随机增加1-2道题目
            Random random = new Random();
            int additionalQuestions = random.nextInt(2) + 1; // 随机生成1或2
            Integer finalQuestionCount = baseQuestionCount + additionalQuestions;

            log.info("知识点 {} 最终题目数量: {} (基础配置: {} + 随机增加: {})",
                     knowledgePointId, finalQuestionCount, baseQuestionCount, additionalQuestions);

            // 获取知识点的所有题目
            List<Question> allQuestions = questionRepository.findByKnowledgePointId(knowledgePointId);
            log.debug("知识点 {} 共有 {} 道题目", knowledgePointId, allQuestions.size());

            if (allQuestions.isEmpty()) {
                log.warn("知识点 {} 没有题目，返回空列表", knowledgePointId);
                return Collections.emptyList();
            }

            // 确保最终题目数量不超过该知识点的总题目数量
            if (allQuestions.size() <= finalQuestionCount) {
                log.info("知识点 {} 题目总数 {} 小于等于最终需要数量 {}，返回所有题目",
                         knowledgePointId, allQuestions.size(), finalQuestionCount);
                return allQuestions;
            }

            // 根据策略选择题目 - 使用自适应策略
            String strategy = studentId != null ? "ADAPTIVE" : "RANDOM";
            List<Question> selectedQuestions = selectQuestionsByStrategy(allQuestions, finalQuestionCount, studentId, strategy);
            log.info("知识点 {} 成功选择了 {} 道练习题目 (基础配置: {} + 随机增加: {})",
                     knowledgePointId, selectedQuestions.size(), baseQuestionCount, additionalQuestions);

            return selectedQuestions;

        } catch (Exception e) {
            log.error("为知识点 {} 选择练习题目时发生错误: {}", knowledgePointId, e.getMessage(), e);
            // 返回空列表而不是抛出异常，确保系统稳定性
            return Collections.emptyList();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<Question> selectQuestionsForChapterTest(Long chapterId, Long studentId) {
        log.info("开始为章节 {} 选择测试题目，学生ID: {}", chapterId, studentId);

        try {
            // 参数验证
            if (chapterId == null) {
                log.error("章节ID不能为空");
                throw new IllegalArgumentException("章节ID不能为空");
            }

            // 获取章节下的所有知识点
            List<KnowledgePoint> knowledgePoints = knowledgePointRepository.findByChapterIdOrderByOrderIndexAsc(chapterId);
            log.debug("章节 {} 下共有 {} 个知识点", chapterId, knowledgePoints.size());

            if (knowledgePoints.isEmpty()) {
                log.warn("章节 {} 下没有知识点，返回空列表", chapterId);
                return Collections.emptyList();
            }

            // 新策略：选择60%数量的知识点，优先选择掌握率低的知识点，但保证绝对随机
            List<KnowledgePoint> selectedKnowledgePoints = selectKnowledgePointsForChapterTest(knowledgePoints, studentId);
            log.info("章节 {} 从 {} 个知识点中选择了 {} 个知识点进行测试",
                    chapterId, knowledgePoints.size(), selectedKnowledgePoints.size());

            List<Question> selectedQuestions = new ArrayList<>();
            int totalAvailableQuestions = 0;

            // 为选中的知识点选择题目
            for (KnowledgePoint knowledgePoint : selectedKnowledgePoints) {
                try {
                    List<Question> knowledgePointQuestions = questionRepository.findByKnowledgePointId(knowledgePoint.getId());
                    totalAvailableQuestions += knowledgePointQuestions.size();

                    if (knowledgePointQuestions.isEmpty()) {
                        log.debug("知识点 {} 没有题目，跳过", knowledgePoint.getId());
                        continue;
                    }

                    // 计算该知识点应该抽取的题目数量
                    Integer questionCount = calculateChapterTestQuestionCount(knowledgePoint.getId(), knowledgePointQuestions.size());
                    log.debug("知识点 {} 计划抽取 {} 道题目（总共 {} 道）",
                             knowledgePoint.getId(), questionCount, knowledgePointQuestions.size());

                    // 选择题目 - 章节测试使用自适应策略
                    String strategy = studentId != null ? "ADAPTIVE" : "RANDOM";
                    List<Question> selected = selectQuestionsByStrategy(knowledgePointQuestions, questionCount, studentId, strategy);
                    selectedQuestions.addAll(selected);

                    log.debug("知识点 {} 实际选择了 {} 道题目", knowledgePoint.getId(), selected.size());

                } catch (Exception e) {
                    log.error("为知识点 {} 选择题目时发生错误: {}", knowledgePoint.getId(), e.getMessage(), e);
                    // 继续处理其他知识点，不中断整个流程
                }
            }

            log.info("章节 {} 初步选择了 {} 道题目（总可用题目: {}）", chapterId, selectedQuestions.size(), totalAvailableQuestions);

            // 如果总题目数超过限制，进行二次筛选
            if (selectedQuestions.size() > MAX_CHAPTER_TEST_TOTAL) {
                log.info("章节测试题目总数 {} 超过限制 {}，进行二次筛选", selectedQuestions.size(), MAX_CHAPTER_TEST_TOTAL);
                selectedQuestions = selectQuestionsByStrategy(selectedQuestions, MAX_CHAPTER_TEST_TOTAL, studentId, "RANDOM");
                log.info("二次筛选后保留 {} 道题目", selectedQuestions.size());
            }

            log.info("章节 {} 最终选择了 {} 道测试题目", chapterId, selectedQuestions.size());
            return selectedQuestions;

        } catch (Exception e) {
            log.error("为章节 {} 选择测试题目时发生错误: {}", chapterId, e.getMessage(), e);
            // 返回空列表而不是抛出异常，确保系统稳定性
            return Collections.emptyList();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<Question> selectQuestionsByCount(Long knowledgePointId, Integer questionCount, 
                                               Long studentId, String selectionStrategy) {
        List<Question> allQuestions = questionRepository.findByKnowledgePointId(knowledgePointId);
        
        if (allQuestions.isEmpty() || questionCount <= 0) {
            return Collections.emptyList();
        }

        if (allQuestions.size() <= questionCount) {
            return allQuestions;
        }

        return selectQuestionsByStrategy(allQuestions, questionCount, studentId, selectionStrategy);
    }

    @Override
    @Transactional(readOnly = true)
    public Integer getEffectivePracticeCount(Long knowledgePointId) {
        log.debug("获取知识点 {} 的有效练习题目数量配置", knowledgePointId);

        try {
            // 参数验证
            if (knowledgePointId == null) {
                log.error("知识点ID不能为空");
                return DEFAULT_KNOWLEDGE_POINT_PRACTICE_COUNT;
            }

            // 获取知识点信息
            Optional<KnowledgePoint> knowledgePointOpt = knowledgePointRepository.findById(knowledgePointId);
            if (!knowledgePointOpt.isPresent()) {
                log.warn("知识点 {} 不存在，使用默认配置", knowledgePointId);
                return DEFAULT_KNOWLEDGE_POINT_PRACTICE_COUNT;
            }

            KnowledgePoint knowledgePoint = knowledgePointOpt.get();

            // 1. 优先级最高：检查知识点自定义配置
            Integer customCount = knowledgePoint.getEffectivePracticeCount();
            if (customCount != null) {
                log.info("知识点 {} 使用知识点自定义配置: {} 道题", knowledgePointId, customCount);
                return customCount;
            }

            // 2. 查询配置表中的配置（按优先级：知识点 > 章节 > 科目 > 全局）
            Integer configCount = getConfiguredPracticeCount(knowledgePoint);
            if (configCount != null) {
                return configCount;
            }

            // 3. 最后使用系统默认配置
            log.info("知识点 {} 使用系统默认配置: {} 道题", knowledgePointId, DEFAULT_KNOWLEDGE_POINT_PRACTICE_COUNT);
            return DEFAULT_KNOWLEDGE_POINT_PRACTICE_COUNT;

        } catch (Exception e) {
            log.error("获取知识点 {} 的有效练习配置时发生错误: {}", knowledgePointId, e.getMessage(), e);
            // 返回默认值确保系统稳定性
            return DEFAULT_KNOWLEDGE_POINT_PRACTICE_COUNT;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Map<Long, Integer> getChapterTestQuestionAllocation(Long chapterId) {
        List<KnowledgePoint> knowledgePoints = knowledgePointRepository.findByChapterIdOrderByOrderIndexAsc(chapterId);
        Map<Long, Integer> allocation = new HashMap<>();
        
        for (KnowledgePoint knowledgePoint : knowledgePoints) {
            int totalQuestions = questionRepository.countByKnowledgePointId(knowledgePoint.getId());
            if (totalQuestions > 0) {
                Integer questionCount = calculateChapterTestQuestionCount(knowledgePoint.getId(), totalQuestions);
                allocation.put(knowledgePoint.getId(), questionCount);
            }
        }
        
        return allocation;
    }

    @Override
    @Transactional(readOnly = true)
    public Integer calculateChapterTestQuestionCount(Long knowledgePointId, Integer totalQuestions) {
        if (totalQuestions == null || totalQuestions <= 0) {
            return 0;
        }

        // 使用固定规则：总题目数 ÷ 10，最小值为1
        Integer calculatedCount = Math.max(totalQuestions / DEFAULT_CHAPTER_TEST_DIVISOR, MIN_CHAPTER_TEST_QUESTIONS);
        
        log.debug("知识点 {} 总题目数: {}，计算得出抽题数量: {}", knowledgePointId, totalQuestions, calculatedCount);
        return calculatedCount;
    }

    /**
     * 根据策略选择题目
     */
    private List<Question> selectQuestionsByStrategy(List<Question> questions, Integer count,
                                                   Long studentId, String strategy) {
        log.debug("使用策略 {} 从 {} 道题目中选择 {} 道题目", strategy, questions.size(), count);

        try {
            // 参数验证
            if (questions == null || questions.isEmpty()) {
                log.warn("题目列表为空，返回空列表");
                return Collections.emptyList();
            }

            if (count == null || count <= 0) {
                log.warn("题目数量配置无效: {}，返回空列表", count);
                return Collections.emptyList();
            }

            if (questions.size() <= count) {
                log.debug("题目总数 {} 小于等于需要数量 {}，返回所有题目", questions.size(), count);
                return new ArrayList<>(questions);
            }

            // 策略选择
            List<Question> selectedQuestions;
            switch (strategy.toUpperCase()) {
                case "ERROR_PRIORITY":
                    selectedQuestions = selectWithErrorPriority(questions, count, studentId);
                    break;
                case "DIFFICULTY_BALANCED":
                    selectedQuestions = selectWithDifficultyBalance(questions, count);
                    break;
                case "TYPE_BALANCED":
                    selectedQuestions = selectWithTypeBalance(questions, count);
                    break;
                case "ADAPTIVE":
                    selectedQuestions = selectWithAdaptiveStrategy(questions, count, studentId);
                    break;
                case "RANDOM":
                default:
                    selectedQuestions = selectRandomly(questions, count);
                    break;
            }

            log.debug("策略 {} 成功选择了 {} 道题目", strategy, selectedQuestions.size());
            return selectedQuestions;

        } catch (Exception e) {
            log.error("使用策略 {} 选择题目时发生错误: {}", strategy, e.getMessage(), e);
            // 降级到随机选择策略
            try {
                log.info("降级使用随机选择策略");
                return selectRandomly(questions, count);
            } catch (Exception fallbackError) {
                log.error("随机选择策略也失败: {}", fallbackError.getMessage(), fallbackError);
                // 最后的保险：返回前N道题目
                int safeCount = Math.min(count, questions.size());
                return new ArrayList<>(questions.subList(0, safeCount));
            }
        }
    }

    /**
     * 随机选择题目
     * 使用基于时间和学生ID的随机种子，确保每次练习都有不同的题目顺序
     */
    private List<Question> selectRandomly(List<Question> questions, Integer count) {
        try {
            if (questions == null || questions.isEmpty() || count == null || count <= 0) {
                return Collections.emptyList();
            }

            List<Question> shuffled = new ArrayList<>(questions);

            // 使用基于当前时间的随机种子，确保每次调用都产生不同的结果
            Random random = new Random(System.currentTimeMillis());
            Collections.shuffle(shuffled, random);

            int actualCount = Math.min(count, shuffled.size());

            log.debug("随机选择策略：从 {} 道题目中随机选择 {} 道，使用时间种子: {}",
                     questions.size(), actualCount, System.currentTimeMillis());
            return shuffled.subList(0, actualCount);

        } catch (Exception e) {
            log.error("随机选择题目时发生错误: {}", e.getMessage(), e);
            // 最后的保险：返回前N道题目
            int safeCount = Math.min(count != null ? count : 0, questions != null ? questions.size() : 0);
            if (safeCount > 0 && questions != null) {
                return new ArrayList<>(questions.subList(0, safeCount));
            }
            return Collections.emptyList();
        }
    }

    /**
     * 错题优先选择
     */
    private List<Question> selectWithErrorPriority(List<Question> questions, Integer count, Long studentId) {
        // 获取学生的错题ID列表
        Set<Long> wrongQuestionIds = studentAnswerRepository
            .findByStudentIdAndIsCorrectFalse(studentId)
            .stream()
            .map(answer -> answer.getQuestion().getId())
            .collect(Collectors.toSet());

        // 分离错题和其他题目
        List<Question> wrongQuestions = questions.stream()
            .filter(q -> wrongQuestionIds.contains(q.getId()))
            .collect(Collectors.toList());
        
        List<Question> otherQuestions = questions.stream()
            .filter(q -> !wrongQuestionIds.contains(q.getId()))
            .collect(Collectors.toList());

        List<Question> selected = new ArrayList<>();
        Random random = new Random(System.currentTimeMillis());

        // 优先选择错题
        Collections.shuffle(wrongQuestions, random);
        int wrongCount = Math.min(wrongQuestions.size(), count);
        selected.addAll(wrongQuestions.subList(0, wrongCount));

        // 如果错题不够，补充其他题目
        int remainingCount = count - selected.size();
        if (remainingCount > 0 && !otherQuestions.isEmpty()) {
            Collections.shuffle(otherQuestions, random);
            int otherCount = Math.min(otherQuestions.size(), remainingCount);
            selected.addAll(otherQuestions.subList(0, otherCount));
        }
        
        return selected;
    }

    /**
     * 难度均衡选择（如果题目有难度信息）
     */
    private List<Question> selectWithDifficultyBalance(List<Question> questions, Integer count) {
        // 简化实现：按难度分组，每组选择相同数量
        Map<String, List<Question>> difficultyGroups = questions.stream()
            .collect(Collectors.groupingBy(q -> 
                q.getDifficulty() != null ? q.getDifficulty() : "UNKNOWN"));
        
        List<Question> selected = new ArrayList<>();
        int perGroupCount = Math.max(1, count / difficultyGroups.size());
        Random random = new Random(System.currentTimeMillis());

        for (List<Question> group : difficultyGroups.values()) {
            Collections.shuffle(group, random);
            int selectCount = Math.min(group.size(), perGroupCount);
            selected.addAll(group.subList(0, selectCount));

            if (selected.size() >= count) {
                break;
            }
        }

        // 如果还不够，随机补充
        if (selected.size() < count) {
            List<Question> remaining = questions.stream()
                .filter(q -> !selected.contains(q))
                .collect(Collectors.toList());
            Collections.shuffle(remaining, random);
            int needMore = count - selected.size();
            selected.addAll(remaining.subList(0, Math.min(remaining.size(), needMore)));
        }
        
        return selected.subList(0, Math.min(selected.size(), count));
    }

    /**
     * 题型均衡选择
     */
    private List<Question> selectWithTypeBalance(List<Question> questions, Integer count) {
        // 按题型分组
        Map<Question.QuestionType, List<Question>> typeGroups = questions.stream()
            .collect(Collectors.groupingBy(Question::getQuestionType));
        
        List<Question> selected = new ArrayList<>();
        int perTypeCount = Math.max(1, count / typeGroups.size());
        Random random = new Random(System.currentTimeMillis());

        for (List<Question> group : typeGroups.values()) {
            Collections.shuffle(group, random);
            int selectCount = Math.min(group.size(), perTypeCount);
            selected.addAll(group.subList(0, selectCount));

            if (selected.size() >= count) {
                break;
            }
        }

        // 如果还不够，随机补充
        if (selected.size() < count) {
            List<Question> remaining = questions.stream()
                .filter(q -> !selected.contains(q))
                .collect(Collectors.toList());
            Collections.shuffle(remaining, random);
            int needMore = count - selected.size();
            selected.addAll(remaining.subList(0, Math.min(remaining.size(), needMore)));
        }
        
        return selected.subList(0, Math.min(selected.size(), count));
    }

    /**
     * 从配置表中获取练习题目数量配置
     * 按优先级查询：知识点 > 章节 > 科目 > 全局
     */
    private Integer getConfiguredPracticeCount(KnowledgePoint knowledgePoint) {
        try {
            // 获取层级ID信息
            Long knowledgePointId = knowledgePoint.getId();
            Long chapterId = null;
            Long subjectId = null;

            // 安全获取章节ID和科目ID
            if (knowledgePoint.getChapter() != null) {
                chapterId = knowledgePoint.getChapter().getId();
                if (knowledgePoint.getChapter().getSubjectVersion() != null
                    && knowledgePoint.getChapter().getSubjectVersion().getSubject() != null) {
                    subjectId = knowledgePoint.getChapter().getSubjectVersion().getSubject().getId();
                }
            }

            log.debug("查询配置 - 知识点ID: {}, 章节ID: {}, 科目ID: {}", knowledgePointId, chapterId, subjectId);

            // 使用Repository查询有效配置（按优先级排序）
            List<PracticeConfig> configs = practiceConfigRepository.findEffectiveConfigsForKnowledgePoint(
                PracticeConfig.ConfigType.KNOWLEDGE_POINT_PRACTICE,
                knowledgePointId,
                chapterId,
                subjectId
            );

            // 返回第一个匹配的配置（优先级最高）
            if (!configs.isEmpty()) {
                PracticeConfig config = configs.get(0);
                Integer questionCount = config.getQuestionCount();
                log.info("知识点 {} 使用{}配置: {} 道题",
                    knowledgePointId, config.getScopeType().name(), questionCount);
                return questionCount;
            }

            log.debug("知识点 {} 在配置表中未找到匹配的配置", knowledgePointId);
            return null;

        } catch (Exception e) {
            log.error("查询知识点 {} 的配置表配置时发生错误: {}", knowledgePoint.getId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 自适应题目选择策略
     * 根据学生的答题正确率动态调整题目难度和类型
     *
     * @param questions 题目列表
     * @param count 需要选择的题目数量
     * @param studentId 学生ID
     * @return 选择的题目列表
     */
    private List<Question> selectWithAdaptiveStrategy(List<Question> questions, Integer count, Long studentId) {
        log.debug("使用自适应策略选择题目，学生ID: {}, 题目总数: {}, 需要选择: {}", studentId, questions.size(), count);

        if (studentId == null) {
            log.debug("学生ID为空，降级使用随机策略");
            return selectRandomly(questions, count);
        }

        try {
            // 计算学生的整体正确率
            Double overallCorrectRate = studentAnswerRepository.calculateOverallCorrectRateByStudentId(studentId);
            if (overallCorrectRate == null) {
                log.debug("学生 {} 没有答题记录，使用随机策略", studentId);
                return selectRandomly(questions, count);
            }

            log.debug("学生 {} 的整体正确率: {:.1f}%", studentId, overallCorrectRate);

            // 根据正确率调整策略
            if (overallCorrectRate >= 80.0) {
                // 正确率高：推送更有挑战性的题目
                log.debug("正确率高(>=80%)，优先选择难题和错题");
                return selectChallengingQuestions(questions, count, studentId);
            } else if (overallCorrectRate >= 60.0) {
                // 正确率中等：平衡选择
                log.debug("正确率中等(60-80%)，平衡选择题目");
                return selectBalancedQuestions(questions, count, studentId);
            } else {
                // 正确率低：推送基础题目
                log.debug("正确率低(<60%)，优先选择基础题目");
                return selectBasicQuestions(questions, count, studentId);
            }

        } catch (Exception e) {
            log.error("自适应策略执行失败，降级使用随机策略: {}", e.getMessage(), e);
            return selectRandomly(questions, count);
        }
    }

    /**
     * 选择有挑战性的题目（正确率高的学生）
     */
    private List<Question> selectChallengingQuestions(List<Question> questions, Integer count, Long studentId) {
        // 获取错题（需要强化的题目）
        Set<Long> wrongQuestionIds = studentAnswerRepository
            .findByStudentIdAndIsCorrectFalse(studentId)
            .stream()
            .map(answer -> answer.getQuestion().getId())
            .collect(Collectors.toSet());

        List<Question> wrongQuestions = questions.stream()
            .filter(q -> wrongQuestionIds.contains(q.getId()))
            .collect(Collectors.toList());

        List<Question> otherQuestions = questions.stream()
            .filter(q -> !wrongQuestionIds.contains(q.getId()))
            .collect(Collectors.toList());

        List<Question> selected = new ArrayList<>();

        // 70%选择错题，30%选择其他题目
        int wrongCount = Math.min(wrongQuestions.size(), (int) (count * 0.7));
        int otherCount = count - wrongCount;

        // 使用基于时间的随机种子进行随机选择
        Random random = new Random(System.currentTimeMillis());

        // 随机选择错题
        Collections.shuffle(wrongQuestions, random);
        selected.addAll(wrongQuestions.subList(0, Math.min(wrongCount, wrongQuestions.size())));

        // 随机选择其他题目
        Collections.shuffle(otherQuestions, random);
        selected.addAll(otherQuestions.subList(0, Math.min(otherCount, otherQuestions.size())));

        // 如果还不够，补充剩余题目
        if (selected.size() < count) {
            List<Question> remaining = questions.stream()
                .filter(q -> !selected.contains(q))
                .collect(Collectors.toList());
            Collections.shuffle(remaining, random);
            int needed = count - selected.size();
            selected.addAll(remaining.subList(0, Math.min(needed, remaining.size())));
        }

        Collections.shuffle(selected, random);
        return selected.subList(0, Math.min(count, selected.size()));
    }

    /**
     * 平衡选择题目（正确率中等的学生）
     */
    private List<Question> selectBalancedQuestions(List<Question> questions, Integer count, Long studentId) {
        // 50%错题优先，50%随机选择
        return selectWithErrorPriority(questions, count, studentId);
    }

    /**
     * 选择基础题目（正确率低的学生）
     */
    private List<Question> selectBasicQuestions(List<Question> questions, Integer count, Long studentId) {
        // 获取学生已经答对的题目
        Set<Long> correctQuestionIds = studentAnswerRepository
            .findByStudentIdAndIsCorrectTrue(studentId)
            .stream()
            .map(answer -> answer.getQuestion().getId())
            .collect(Collectors.toSet());

        // 优先选择学生还没有答对的题目（可能是基础题目）
        List<Question> unsolvedQuestions = questions.stream()
            .filter(q -> !correctQuestionIds.contains(q.getId()))
            .collect(Collectors.toList());

        Random random = new Random(System.currentTimeMillis());

        if (unsolvedQuestions.size() >= count) {
            Collections.shuffle(unsolvedQuestions, random);
            return unsolvedQuestions.subList(0, count);
        } else {
            // 如果未解决的题目不够，补充一些已答对的题目进行复习
            List<Question> selected = new ArrayList<>(unsolvedQuestions);
            List<Question> solvedQuestions = questions.stream()
                .filter(q -> correctQuestionIds.contains(q.getId()))
                .collect(Collectors.toList());

            Collections.shuffle(solvedQuestions, random);
            int needed = count - selected.size();
            selected.addAll(solvedQuestions.subList(0, Math.min(needed, solvedQuestions.size())));

            Collections.shuffle(selected, random);
            return selected.subList(0, Math.min(count, selected.size()));
        }
    }

    /**
     * 为章节测试选择知识点
     * 新策略：选择60%数量的知识点，优先选择掌握率低的知识点，但保证绝对随机
     *
     * @param allKnowledgePoints 所有知识点
     * @param studentId 学生ID
     * @return 选中的知识点列表
     */
    private List<KnowledgePoint> selectKnowledgePointsForChapterTest(List<KnowledgePoint> allKnowledgePoints, Long studentId) {
        log.info("开始为章节测试选择知识点，总知识点数: {}, 学生ID: {}", allKnowledgePoints.size(), studentId);

        if (allKnowledgePoints.isEmpty()) {
            return Collections.emptyList();
        }

        // 计算需要选择的知识点数量（60%，至少1个）
        // 使用四舍五入而不是向上取整，确保真正的60%比例
        int targetCount = Math.max(1, Math.round(allKnowledgePoints.size() * 0.6f));
        log.debug("计划选择 {} 个知识点（总数: {}，60%比例）", targetCount, allKnowledgePoints.size());

        // 如果需要选择的数量等于或大于总数，直接返回所有知识点
        if (targetCount >= allKnowledgePoints.size()) {
            log.debug("需要选择的知识点数量 {} >= 总数 {}，返回所有知识点", targetCount, allKnowledgePoints.size());
            return new ArrayList<>(allKnowledgePoints);
        }

        try {
            // 为每个知识点计算选择权重（用作概率）
            List<KnowledgePointWithWeight> weightedKnowledgePoints = new ArrayList<>();
            double totalWeight = 0.0;

            for (KnowledgePoint kp : allKnowledgePoints) {
                double masteryRate = 0.0;

                if (studentId != null) {
                    // 获取学生在该知识点的掌握率
                    Double masteryRateFromDb = studentAnswerRepository.calculateCorrectRateByStudentIdAndKnowledgePointId(studentId, kp.getId());
                    if (masteryRateFromDb != null) {
                        masteryRate = masteryRateFromDb / 100.0; // 转换为0-1范围
                    }
                }

                // 计算选择权重：掌握率越低，权重越高
                // 权重将用作选择概率，不再使用随机因子
                double weight = 1.0 - masteryRate + 0.1; // 加0.1确保即使掌握率100%也有被选中的机会
                totalWeight += weight;

                weightedKnowledgePoints.add(new KnowledgePointWithWeight(kp, weight, masteryRate));

                log.debug("知识点 {} 掌握率: {:.1f}%, 选择权重: {:.3f}",
                         kp.getId(), masteryRate * 100, weight);
            }

            // 使用加权随机抽样进行真正的随机选择
            List<KnowledgePoint> selectedKnowledgePoints = performWeightedRandomSampling(
                    weightedKnowledgePoints, totalWeight, targetCount);

            // 记录选择结果
            log.info("知识点随机选择完成，选中 {} 个知识点:", selectedKnowledgePoints.size());
            for (KnowledgePoint kp : selectedKnowledgePoints) {
                // 找到对应的权重信息
                KnowledgePointWithWeight weighted = weightedKnowledgePoints.stream()
                        .filter(w -> w.getKnowledgePoint().getId().equals(kp.getId()))
                        .findFirst().orElse(null);
                if (weighted != null) {
                    log.info("  选中知识点 {}: 掌握率 {:.1f}%, 权重 {:.3f}",
                            kp.getId(), weighted.getMasteryRate() * 100, weighted.getWeight());
                }
            }

            return selectedKnowledgePoints;

        } catch (Exception e) {
            log.error("选择知识点时发生错误: {}", e.getMessage(), e);
            // 发生错误时，随机选择知识点作为降级策略
            log.warn("使用降级策略：随机选择知识点");
            List<KnowledgePoint> shuffled = new ArrayList<>(allKnowledgePoints);
            Random random = new Random(System.currentTimeMillis());
            Collections.shuffle(shuffled, random);
            return shuffled.subList(0, targetCount);
        }
    }

    /**
     * 执行加权随机抽样
     * 使用轮盘赌算法进行真正的随机选择，确保每个知识点都有被选中的概率
     *
     * @param weightedKnowledgePoints 带权重的知识点列表
     * @param totalWeight 总权重
     * @param targetCount 目标选择数量
     * @return 随机选中的知识点列表
     */
    private List<KnowledgePoint> performWeightedRandomSampling(
            List<KnowledgePointWithWeight> weightedKnowledgePoints,
            double totalWeight,
            int targetCount) {

        List<KnowledgePoint> selectedKnowledgePoints = new ArrayList<>();
        Set<Integer> selectedIndices = new HashSet<>();

        // 进行targetCount次随机抽样
        while (selectedKnowledgePoints.size() < targetCount && selectedIndices.size() < weightedKnowledgePoints.size()) {
            double random = Math.random() * totalWeight;
            double cumulative = 0.0;

            // 轮盘赌选择
            for (int i = 0; i < weightedKnowledgePoints.size(); i++) {
                if (selectedIndices.contains(i)) {
                    continue; // 跳过已选中的知识点
                }

                cumulative += weightedKnowledgePoints.get(i).getWeight();
                if (random <= cumulative) {
                    selectedIndices.add(i);
                    selectedKnowledgePoints.add(weightedKnowledgePoints.get(i).getKnowledgePoint());

                    // 从总权重中减去已选中知识点的权重，确保下次抽样的准确性
                    totalWeight -= weightedKnowledgePoints.get(i).getWeight();

                    log.debug("随机选中知识点 {} (索引 {}), 剩余权重: {:.3f}",
                             weightedKnowledgePoints.get(i).getKnowledgePoint().getId(), i, totalWeight);
                    break;
                }
            }

            // 防止无限循环：如果所有知识点都被选中了，退出
            if (selectedIndices.size() >= weightedKnowledgePoints.size()) {
                break;
            }
        }

        log.info("加权随机抽样完成，目标 {} 个，实际选中 {} 个知识点", targetCount, selectedKnowledgePoints.size());
        return selectedKnowledgePoints;
    }

    /**
     * 带权重的知识点包装类
     */
    private static class KnowledgePointWithWeight {
        private final KnowledgePoint knowledgePoint;
        private final double weight;
        private final double masteryRate;

        public KnowledgePointWithWeight(KnowledgePoint knowledgePoint, double weight, double masteryRate) {
            this.knowledgePoint = knowledgePoint;
            this.weight = weight;
            this.masteryRate = masteryRate;
        }

        public KnowledgePoint getKnowledgePoint() {
            return knowledgePoint;
        }

        public double getWeight() {
            return weight;
        }

        public double getMasteryRate() {
            return masteryRate;
        }
    }
}
