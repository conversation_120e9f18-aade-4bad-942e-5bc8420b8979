/**
 * 统一填空渲染器
 * 所有下划线填空统一使用MathQuill输入框和数学键盘
 */

import React, { useState, useEffect, useRef, useCallback, useMemo, useContext } from 'react';
import PropTypes from 'prop-types';
import { Alert } from 'antd';
import { MathJax, MathJaxBaseContext } from 'better-react-mathjax';

import BlankDetector from './BlankDetector';
import AnswerValidator, { SubjectType, ValidationStatus } from './AnswerValidator';
import { mathLiveInputManager } from './MathLiveInputManager';

import './UnifiedBlankRenderer.css';
import './MathQuillKeyboard.css';

// 防抖工具函数
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 简单的答案验证fallback函数
const simpleValidateAnswer = (userAnswer, correctAnswer) => {
  if (!userAnswer || userAnswer.trim() === '') {
    return ValidationStatus.EMPTY;
  }

  if (!correctAnswer) {
    return ValidationStatus.EMPTY;
  }

  // 简单的字符串比较
  const normalizedUser = userAnswer.toString().trim().toLowerCase();
  const normalizedCorrect = correctAnswer.toString().trim().toLowerCase();

  return normalizedUser === normalizedCorrect ? ValidationStatus.CORRECT : ValidationStatus.INCORRECT;
};



// 优化的渲染器Hook
const useOptimizedRenderer = (htmlContent) => {
  // 移除options参数，简化接口
  const [state, setState] = useState({
    processedHtml: '',
    blanks: new Map(),
    blankList: [],
    isProcessing: false,
    error: null
  });

  const detector = useMemo(() => new BlankDetector({ enableDebug: false }), []); // 关闭调试，避免循环日志

  useEffect(() => {
    if (!htmlContent) {
      setState({
        processedHtml: '',
        blanks: new Map(),
        blankList: [],
        isProcessing: false,
        error: null
      });
      return;
    }

    setState(prev => ({ ...prev, isProcessing: true, error: null }));

    try {
      const result = detector.detect(htmlContent);

      // 移除调试日志，避免循环输出
      // if (enableDebug) {
      //   console.log('填空检测结果:', result);
      // }

      setState({
        processedHtml: result.processedHtml,
        blanks: result.blanks,
        blankList: result.blankList,
        isProcessing: false,
        error: null
      });
    } catch (error) {
      console.error('填空处理错误:', error);
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: error.message
      }));
    }
  }, [htmlContent, detector]); // 移除enableDebug依赖，避免循环

  return state;
};

// 主要的UnifiedBlankRenderer组件
const UnifiedBlankRenderer = React.memo(({
  htmlContent,
  answers = {},
  onAnswerChange,
  showResult = false,
  result = null,
  subject = 'math',
  disabled = false,
  onFocus,
  onBlur
}) => {
  const mathJaxContext = useContext(MathJaxBaseContext);
  const validator = useMemo(() => {
    try {
      const instance = new AnswerValidator();
      // 验证实例是否正确创建
      if (typeof instance.validateAnswer !== 'function') {
        console.error('AnswerValidator实例创建失败：validateAnswer方法不存在');
        console.log('可用方法:', Object.getOwnPropertyNames(Object.getPrototypeOf(instance)));
        return null;
      }
      return instance;
    } catch (error) {
      console.error('AnswerValidator实例化失败:', error);
      return null;
    }
  }, []);
  const containerRef = useRef(null);
  const inputInstancesRef = useRef(new Map());
  const debouncedAnswerChangeRef = useRef(null);

  // 存储当前所有填空的答案
  const currentAnswersRef = useRef(answers || {});

  // 同步外部传入的answers到内部状态
  useEffect(() => {
    currentAnswersRef.current = { ...answers };
  }, [answers]);

  // 使用ref存储当前值，避免useEffect频繁触发
  const currentValuesRef = useRef({ answers, result, showResult, disabled });
  currentValuesRef.current = { answers, result, showResult, disabled };

  const { processedHtml, blanks, blankList, isProcessing, error } = useOptimizedRenderer(
    htmlContent
  );



  // 创建稳定的防抖函数引用 - 修改为收集所有答案后传递
  if (!debouncedAnswerChangeRef.current && onAnswerChange) {
    debouncedAnswerChangeRef.current = debounce(() => {


      // 传递完整的答案对象
      onAnswerChange(currentAnswersRef.current);
    }, 300);
  }

  // 更新防抖函数的回调
  useEffect(() => {
    if (onAnswerChange && debouncedAnswerChangeRef.current) {
      debouncedAnswerChangeRef.current = debounce(() => {


        // 传递完整的答案对象
        onAnswerChange(currentAnswersRef.current);
      }, 300);
    }
  }, [onAnswerChange]);

  // 后处理LaTeX代码，确保正确的括号格式
  const postProcessLatex = useCallback((latex) => {
    if (!latex || typeof latex !== 'string') return latex;

    let processed = latex;

    // 修复分数格式：\frac43 -> \frac{4}{3}
    // 处理两个连续的非括号字符
    processed = processed.replace(/\\frac([^{\\])([^{\\])/g, '\\frac{$1}{$2}');

    // 处理单个字符后跟数字的情况
    processed = processed.replace(/\\frac([^{\\])(\d+)/g, '\\frac{$1}{$2}');
    processed = processed.replace(/\\frac(\d+)([^{\\])/g, '\\frac{$1}{$2}');

    // 修复上标格式：确保多字符上标有括号
    processed = processed.replace(/\^([a-zA-Z0-9]{2,})/g, '^{$1}');
    processed = processed.replace(/\^([^{\\])([a-zA-Z0-9]+)/g, '^{$1$2}');

    // 修复下标格式：确保多字符下标有括号
    processed = processed.replace(/_([a-zA-Z0-9]{2,})/g, '_{$1}');
    processed = processed.replace(/_([^{\\])([a-zA-Z0-9]+)/g, '_{$1$2}');

    // 修复根号格式：\sqrt2 -> \sqrt{2}
    processed = processed.replace(/\\sqrt([^{\\][^\\]*?)(\s|$|\\)/g, '\\sqrt{$1}$2');

    // 修复其他常见函数的参数格式
    const functions = ['sin', 'cos', 'tan', 'log', 'ln', 'exp', 'lim', 'max', 'min'];
    functions.forEach(func => {
      const regex = new RegExp(`\\\\${func}([^{\\s\\\\][\\w\\d]*)`, 'g');
      processed = processed.replace(regex, `\\${func}{$1}`);
    });

    return processed;
  }, []);

  const handleAnswerChange = useCallback((blankId, value) => {
    // 🔧 对LaTeX值进行后处理
    const processedValue = postProcessLatex(value);

    // 更新当前答案（使用后处理的值）
    currentAnswersRef.current = {
      ...currentAnswersRef.current,
      [blankId]: processedValue
    };

    // 触发防抖函数
    if (debouncedAnswerChangeRef.current) {
      debouncedAnswerChangeRef.current();
    }
  }, [postProcessLatex]);

  // 修复LaTeX根号格式
  const fixSqrtFormat = useCallback((latex) => {
    if (!latex || typeof latex !== 'string') return latex;

    let processed = latex;

    // 🔧 修复错误的n次根格式：\sqrt[6]{5} -> 5\sqrt{6}
    // 这种格式通常是用户输入"5根号6"被错误解析造成的
    processed = processed.replace(/\\sqrt\[(\d+)\]\{(\d+)\}/g, (match, index, content) => {
      console.log('🔧 UnifiedBlankRenderer修复错误的根号格式:', match, '→', `${content}\\sqrt{${index}}`);
      return `${content}\\sqrt{${index}}`;
    });

    // 修复其他可能的错误格式
    processed = processed.replace(/\\sqrt\[([^\]]+)\]\{([^}]+)\}/g, (match, index, content) => {
      // 如果索引和内容都是简单的数字或字母，可能是错误格式
      if (/^[a-zA-Z0-9]+$/.test(index) && /^[a-zA-Z0-9]+$/.test(content)) {
        console.log('🔧 UnifiedBlankRenderer修复复杂根号格式:', match, '→', `${content}\\sqrt{${index}}`);
        return `${content}\\sqrt{${index}}`;
      }
      return match; // 保持原样
    });

    return processed;
  }, []);

  // 创建并插入输入框到指定位置 - 使用稳定的依赖项
  const createInputAtPlaceholder = useCallback((blankId, placeholderElement, currentAnswers, currentResult, currentShowResult, currentDisabled) => {
    const rawValue = currentAnswers[blankId] || '';
    // 🔧 在显示前修复LaTeX格式
    const value = fixSqrtFormat(rawValue);
    const correctAnswer = currentResult?.correctAnswers?.[blankId];

    // 🔧 验证答案 - 优先使用后端返回的详细结果
    let status = ValidationStatus.EMPTY;
    if (currentShowResult && currentResult) {
      // 优先使用后端返回的详细空格验证结果
      if (currentResult.blankResults && currentResult.blankResults[blankId]) {
        const blankResult = currentResult.blankResults[blankId];
        // 将后端状态转换为前端ValidationStatus
        switch (blankResult.status) {
          case 'correct':
            status = ValidationStatus.CORRECT;
            break;
          case 'incorrect':
            status = ValidationStatus.INCORRECT;
            break;
          case 'empty':
            status = ValidationStatus.EMPTY;
            break;
          default:
            status = ValidationStatus.EMPTY;
        }
      } else {
        // 降级处理：使用前端验证
        try {
          if (validator && typeof validator.validateAnswer === 'function') {
            status = validator.validateAnswer(
              value,
              correctAnswer,
              SubjectType.fromString(subject)
            );
          } else {
            // 使用fallback验证函数
            status = simpleValidateAnswer(value, correctAnswer);
          }
        } catch (error) {
          console.error('答案验证失败:', error);
          // 使用fallback验证函数
          status = simpleValidateAnswer(value, correctAnswer);
        }
      }
    }

    // 创建输入框容器
    const inputContainer = document.createElement('span');
    inputContainer.className = 'unified-input-wrapper';
    inputContainer.dataset.blankId = blankId;

    // 设置宽度（从占位符获取）
    const width = placeholderElement.dataset.blankWidth || '60';
    inputContainer.style.display = 'inline-block';
    inputContainer.style.minWidth = `${width}px`;
    inputContainer.style.cursor = currentDisabled || currentShowResult ? 'default' : 'text';
    inputContainer.style.position = 'relative'; // 为状态图标定位

    // 替换占位符
    placeholderElement.parentNode.replaceChild(inputContainer, placeholderElement);

    // 🔧 智能判断输入类型
    const detectInputType = () => {
      // 检查题目内容是否包含数学符号或公式
      const mathIndicators = [
        '\\', // LaTeX命令
        '^', '_', // 上下标
        '∑', '∫', '√', // 数学符号
        'sin', 'cos', 'tan', 'log', 'ln', // 数学函数
        '≤', '≥', '≠', '≈', // 比较符号
        'π', 'α', 'β', 'γ', 'θ', // 希腊字母
        '∞', '∂', '∇', // 特殊数学符号
        'frac', 'sqrt', 'sum', 'int', // 常见LaTeX命令关键词
      ];

      // 检查当前题目内容
      const questionContent = document.querySelector('.question-content')?.textContent || '';
      const hasMathindicators = mathIndicators.some(indicator =>
        questionContent.includes(indicator)
      );

      // 检查科目类型
      const mathSubjects = ['MATH', 'PHYSICS', 'CHEMISTRY'];
      const isMathSubject = mathSubjects.includes(subject?.toUpperCase());

      // 综合判断：如果是数学科目或包含数学符号，优先数学输入
      return hasMathindicators || isMathSubject ? 'math' : 'device';
    };

    // 🔧 创建双输入框系统：设备输入(IME友好) + 数学键盘输入
    const preferredInputType = detectInputType();
    console.log(`填空 ${blankId} 检测到的输入类型:`, preferredInputType);

    const createDualInputSystem = () => {
      // 创建设备输入框（普通HTML input，支持IME）
      const deviceInput = document.createElement('input');
      deviceInput.type = 'text';
      deviceInput.className = 'blank-device-input';
      deviceInput.setAttribute('data-blank-id', blankId);
      deviceInput.value = value || '';
      deviceInput.placeholder = '';
      deviceInput.disabled = currentDisabled || currentShowResult;

      // 设备输入框样式
      deviceInput.style.cssText = `
        width: 100%;
        height: 32px;
        border: none;
        border-bottom: 2px solid #333;
        background: transparent;
        text-align: center;
        font-size: 16px;
        outline: none;
        padding: 4px 8px;
        box-sizing: border-box;
      `;

      // 创建MathLive输入框（用于数学键盘输入）
      const mathField = document.createElement('math-field');
      mathField.className = 'blank-math-input math-field-input';
      mathField.setAttribute('data-blank-id', blankId);
      mathField.style.cssText = `
        width: 100%;
        height: 32px;
        border: none;
        border-bottom: 2px solid #333;
        background: transparent;
        text-align: center;
        font-size: 16px;
        outline: none;
        padding: 4px 8px;
        box-sizing: border-box;
        display: none;
      `;

      return { deviceInput, mathField };
    };

    const { deviceInput, mathField } = createDualInputSystem();

    // 清空容器并添加两个输入框
    inputContainer.innerHTML = '';
    inputContainer.appendChild(deviceInput);
    inputContainer.appendChild(mathField);

    // 🔧 输入模式状态管理
    let currentInputMode = preferredInputType; // 根据智能检测设置默认模式
    let isFromMathKeyboard = false; // 标记是否来自数学键盘输入

    // 🔧 根据默认模式设置初始显示状态
    if (currentInputMode === 'math') {
      deviceInput.style.display = 'none';
      mathField.style.display = 'block';
    } else {
      deviceInput.style.display = 'block';
      mathField.style.display = 'none';
    }

    // 🔧 输入模式切换函数
    const switchToDeviceInput = () => {
      if (currentInputMode === 'device') return;

      currentInputMode = 'device';
      deviceInput.style.display = 'block';
      mathField.style.display = 'none';
      deviceInput.focus();
      console.log('切换到设备输入模式');
    };

    const switchToMathInput = () => {
      if (currentInputMode === 'math') return;

      currentInputMode = 'math';
      deviceInput.style.display = 'none';
      mathField.style.display = 'block';
      mathField.focus();
      console.log('切换到数学输入模式');
    };

    // 处理设备输入框的输入事件
    const handleDeviceInput = (e) => {
      if (currentDisabled || currentShowResult) return;

      const newValue = e.target.value;
      console.log(`设备输入 ${blankId}:`, newValue);

      // 同步到MathLive输入框
      if (mathField && mathField.setValue) {
        mathField.setValue(newValue);
      }

      handleAnswerChange(blankId, newValue);
    };

    // 处理设备输入框的焦点事件
    const handleDeviceFocus = (e) => {
      if (currentDisabled || currentShowResult) return;

      console.log('设备输入框获得焦点');
      switchToDeviceInput();

      if (onFocus) {
        onFocus(e.target);
      }
    };

    // 绑定设备输入框事件
    deviceInput.addEventListener('input', handleDeviceInput);
    deviceInput.addEventListener('focus', handleDeviceFocus);
    deviceInput.addEventListener('blur', (e) => {
      if (onBlur) onBlur(e);
    });

    // 🔧 添加状态覆盖层处理
    if (currentShowResult && status === ValidationStatus.INCORRECT) {
      const statusOverlay = document.createElement('div');
      statusOverlay.className = 'blank-status-overlay incorrect';
      statusOverlay.style.position = 'absolute';
      statusOverlay.style.top = '0';
      statusOverlay.style.left = '0';
      statusOverlay.style.right = '0';
      statusOverlay.style.bottom = '0';
      statusOverlay.style.display = 'flex';
      statusOverlay.style.alignItems = 'center';
      statusOverlay.style.justifyContent = 'center';
      statusOverlay.style.pointerEvents = 'none'; // 不阻挡点击
      statusOverlay.style.zIndex = '10';

      // 添加红色X图标
      const xIcon = document.createElement('span');
      xIcon.textContent = '✗';
      xIcon.style.color = '#ff4d4f';
      xIcon.style.fontSize = '18px';
      xIcon.style.fontWeight = 'bold';

      statusOverlay.appendChild(xIcon);
      inputContainer.appendChild(statusOverlay);
    }

    if (currentShowResult && status === ValidationStatus.CORRECT) {
      const statusOverlay = document.createElement('div');
      statusOverlay.className = 'blank-status-overlay correct';
      statusOverlay.style.position = 'absolute';
      statusOverlay.style.top = '0';
      statusOverlay.style.left = '0';
      statusOverlay.style.right = '0';
      statusOverlay.style.bottom = '0';
      statusOverlay.style.display = 'flex';
      statusOverlay.style.alignItems = 'center';
      statusOverlay.style.justifyContent = 'center';
      statusOverlay.style.pointerEvents = 'none'; // 不阻挡点击
      statusOverlay.style.zIndex = '10';

      // 添加绿色勾图标
      const checkIcon = document.createElement('span');
      checkIcon.textContent = '✓';
      checkIcon.style.color = '#52c41a';
      checkIcon.style.fontSize = '18px';
      checkIcon.style.fontWeight = 'bold';

      statusOverlay.appendChild(checkIcon);
      inputContainer.appendChild(statusOverlay);
    }

    // 🔧 配置MathLive输入框（专门用于数学键盘输入）
    const inputInfo = mathLiveInputManager.createInput(mathField, {
      subject: 'math',
      isEmbedded: true,
      value: value,
      disabled: currentDisabled || currentShowResult,
      onChange: (newValue, sourceElement) => {
        if (currentDisabled || currentShowResult) return;

        // 🔧 标记来自数学键盘的输入
        isFromMathKeyboard = true;

        // 切换到数学输入模式
        switchToMathInput();

        // 同步到设备输入框
        deviceInput.value = newValue;

        console.log(`数学键盘输入 ${blankId}:`, newValue);
        handleAnswerChange(blankId, newValue);
      },
      onFocus: (mathField) => {
        if (currentDisabled || currentShowResult) return;

        console.log(`MathLive输入框获得焦点 ${blankId}`);

        // 如果不是来自数学键盘，切换到数学输入模式
        if (!isFromMathKeyboard) {
          switchToMathInput();
        }

        if (onFocus) onFocus(blankId, mathField);
        },
        onBlur: () => {
          console.log(`填空 ${blankId} 失去焦点`);
          if (onBlur) onBlur(blankId);
        }
    });

    // 🔧 监听数学键盘输入事件
    const handleMathKeyboardInput = (event) => {
      if (event.detail && event.detail.targetBlankId === blankId) {
        console.log('接收到数学键盘输入事件:', event.detail);
        isFromMathKeyboard = true;
        switchToMathInput();
      }
    };

    // 绑定数学键盘输入事件监听器
    document.addEventListener('mathKeyboardInput', handleMathKeyboardInput);

    // 🔧 容器点击事件处理（双输入框系统）
    const handleContainerClick = (e) => {
      if (currentDisabled || currentShowResult) {
        e.preventDefault();
        e.stopPropagation();
        return;
      }

      console.log('输入框容器点击:', { blankId, target: e.target, currentMode: currentInputMode });
      e.stopPropagation();

      // 🔧 根据当前模式聚焦相应的输入框
      if (currentInputMode === 'device') {
        deviceInput.focus();
      } else {
        mathField.focus();
      }
    };

    // 绑定容器点击事件
    inputContainer.addEventListener('click', handleContainerClick);

    // 🔧 保存双输入框实例信息
    inputInstancesRef.current.set(blankId, {
      type: 'dual', // 标记为双输入框类型
      deviceInput: deviceInput,
      mathField: mathField,
      mathLiveInstance: inputInfo,
      container: inputContainer,
      handleContainerClick: handleContainerClick,
      handleMathKeyboardInput: handleMathKeyboardInput,
      // 🔧 输入模式控制函数
      switchToDeviceInput: switchToDeviceInput,
      switchToMathInput: switchToMathInput,
      getCurrentMode: () => currentInputMode
    });

    return inputContainer;
  }, [subject, validator, onFocus, onBlur, handleAnswerChange, fixSqrtFormat]); // 移除经常变化的依赖项

  // 处理输入框插入到DOM - 只在HTML内容变化时重新创建
  useEffect(() => {
    if (!containerRef.current || !processedHtml || blankList.length === 0) {
      return;
    }

    // 清理之前的输入框
    inputInstancesRef.current.forEach((inputInfo) => {
      if (inputInfo.type === 'mathlive') {
        // 移除容器点击事件
        if (inputInfo.container && inputInfo.handleContainerClick) {
          inputInfo.container.removeEventListener('click', inputInfo.handleContainerClick);
        }
        // 销毁MathLive实例
        mathLiveInputManager.destroyInput(inputInfo.element);
      }
    });
    inputInstancesRef.current.clear();

    // 查找所有占位符并替换为输入框
    const placeholders = containerRef.current.querySelectorAll('[data-blank-id]');
    placeholders.forEach(placeholder => {
      const blankId = placeholder.dataset.blankId;

      // 修复：blanks是普通对象，不是Map
      if (blankId && blanks[blankId]) {
        // 使用ref中的当前值，避免依赖项变化
        const { answers: currentAnswers, result: currentResult, showResult: currentShowResult, disabled: currentDisabled } = currentValuesRef.current;
        createInputAtPlaceholder(blankId, placeholder, currentAnswers, currentResult, currentShowResult, currentDisabled);
      }
    });

    // 清理函数
    return () => {
      inputInstancesRef.current.forEach((inputInfo) => {
        if (inputInfo.type === 'mathlive') {
          // 移除容器点击事件
          if (inputInfo.container && inputInfo.handleContainerClick) {
            inputInfo.container.removeEventListener('click', inputInfo.handleContainerClick);
          }
          // 销毁MathLive实例
          mathLiveInputManager.destroyInput(inputInfo.element);
        }
      });
      inputInstancesRef.current.clear();
    };
  }, [processedHtml, blankList.length, createInputAtPlaceholder, fixSqrtFormat]); // 移除经常变化的依赖项

  // 同步答案值变化 - 只在实际值变化时更新
  const prevAnswersRef = useRef(answers);
  useEffect(() => {
    // 检查是否有实际变化
    const hasChanged = Object.keys(answers).some(blankId =>
      answers[blankId] !== prevAnswersRef.current[blankId]
    ) || Object.keys(prevAnswersRef.current).some(blankId =>
      answers[blankId] !== prevAnswersRef.current[blankId]
    );

    if (!hasChanged) {
      return;
    }

    inputInstancesRef.current.forEach((inputInfo, blankId) => {
      const value = answers[blankId] || '';
      const prevValue = prevAnswersRef.current[blankId] || '';

      // 只在值真正变化时更新
      if (value !== prevValue) {
        if (inputInfo.type === 'mathlive') {
          mathLiveInputManager.setValue(inputInfo.element, value);
        } else if (inputInfo.type === 'input') {
          inputInfo.instance.value = value;
        }
      }
    });

    prevAnswersRef.current = answers;
  }, [answers]);

  // MathJax渲染处理
  useEffect(() => {
    let timeoutIds = []; // 存储定时器ID，用于清理
    let isMounted = true; // 组件挂载状态标记

    if (containerRef.current && processedHtml) {
      const renderMathJax = () => {
        if (!isMounted) return; // 如果组件已卸载，不执行渲染

        if (mathJaxContext && mathJaxContext.promise) {
          mathJaxContext.promise.then((mathJax) => {
            if (!isMounted || !containerRef.current) return; // 再次检查组件状态

            if (mathJax && mathJax.typesetPromise) {
              // 清除之前的MathJax渲染
              if (mathJax.startup && mathJax.startup.document) {
                mathJax.startup.document.clear();
              }

              // 重新渲染
              mathJax.typesetPromise([containerRef.current]).then(() => {
                if (isMounted) {
                  console.log('UnifiedBlankRenderer MathJax渲染成功');
                }
              }).catch((error) => {
                if (isMounted) {
                  console.warn('UnifiedBlankRenderer MathJax渲染失败:', error);
                  // 如果渲染失败，尝试重新渲染
                  const retryTimeoutId = setTimeout(() => {
                    if (isMounted && containerRef.current) {
                      mathJax.typesetPromise([containerRef.current]).catch(console.warn);
                    }
                  }, 500);
                  timeoutIds.push(retryTimeoutId);
                }
              });
            }
          }).catch((error) => {
            if (isMounted) {
              console.warn('MathJax上下文获取失败:', error);
            }
          });
        }
      };

      // 延迟渲染，确保DOM已更新
      const timeoutId1 = setTimeout(renderMathJax, 100);
      timeoutIds.push(timeoutId1);

      // 如果第一次渲染失败，再尝试一次
      const timeoutId2 = setTimeout(renderMathJax, 1000);
      timeoutIds.push(timeoutId2);
    }

    // 清理函数
    return () => {
      isMounted = false; // 标记组件已卸载
      // 清理所有定时器
      timeoutIds.forEach(id => clearTimeout(id));
    };
  }, [processedHtml, mathJaxContext]);

  // 处理错误状态
  if (error) {
    return (
      <Alert
        message="渲染错误"
        description={`填空题渲染失败: ${error}`}
        type="error"
        showIcon
      />
    );
  }

  // 处理加载状态
  if (isProcessing) {
    return <div>正在处理填空题...</div>;
  }

  // 处理空内容
  if (!processedHtml) {
    return <div>暂无内容</div>;
  }

  return (
    <div className="unified-blank-renderer" ref={containerRef}>
      <MathJax>
        <div
          dangerouslySetInnerHTML={{ __html: processedHtml }}
          style={{
            lineHeight: '1.6',
            fontSize: '22px',
            color: '#333'
          }}
        />
      </MathJax>
    </div>
  );
});

// PropTypes定义
UnifiedBlankRenderer.propTypes = {
  htmlContent: PropTypes.string.isRequired,
  answers: PropTypes.object,
  onAnswerChange: PropTypes.func,
  showResult: PropTypes.bool,
  result: PropTypes.object,
  subject: PropTypes.string,
  disabled: PropTypes.bool,
  onFocus: PropTypes.func,
  onBlur: PropTypes.func
};

export default UnifiedBlankRenderer;
