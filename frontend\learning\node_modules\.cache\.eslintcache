[{"D:\\projects\\AIstrusys\\frontend\\learning\\src\\index.js": "1", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\App.js": "2", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\pages\\StudyHome.js": "3", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\pages\\ClassUnit.js": "4", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\apiUtils.js": "5", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\pages\\Login.js": "6", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\pages\\LearningResult.js": "7", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\pages\\LearningTrack.js": "8", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\pages\\WrongQuestionDetail.js": "9", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\pages\\WrongQuestions.js": "10", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\pages\\WrongQuestionElimination.js": "11", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\hooks\\useEyeProtection.js": "12", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\core\\GlobalKeyboardManager.js": "13", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\QuestionRedoModule\\index.js": "14", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\ChapterQuizModule\\index.js": "15", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\LearningModule\\index.js": "16", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\auth.js": "17", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\displayUtils.js": "18", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\questionFormatValidator.js": "19", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\HtmlContentRenderer.js": "20", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\api\\course.js": "21", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\api\\auth.js": "22", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\api\\learning.js": "23", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\api\\wrongQuestions.js": "24", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\api\\user.js": "25", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\api\\intelligentReport.js": "26", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\questionDataProcessor.js": "27", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\common\\QuestionDetailModal.js": "28", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\LearningModule\\scenes\\ResultScreen.js": "29", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\ChapterQuizModule\\scenes\\IntelligentReportModal.js": "30", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\wrongQuestions\\WrongQuestionDateGroup.js": "31", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\common\\index.js": "32", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\LearningModule\\scenes\\QuestionScreen.js": "33", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\core\\MathQuillKeyboard.js": "34", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\ChapterQuizModule\\context.js": "35", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\ChapterQuizModule\\ErrorBoundary.js": "36", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\ChapterQuizModule\\scenes\\ChapterResultScreen.js": "37", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\LearningModule\\context.js": "38", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\LearningModule\\scenes\\VideoScreen.js": "39", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\index.js": "40", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\api\\axios.js": "41", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\LearningModule\\common\\LoadingSpinner.js": "42", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\LearningModule\\common\\ErrorAlert.js": "43", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\index.js": "44", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\core\\UnifiedBlankRenderer.js": "45", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\ContentBlockRenderer.js": "46", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\common\\BackButton.js": "47", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\wrongQuestions\\WrongQuestionCard.js": "48", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\common\\VideoModal.js": "49", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\ChapterQuizModule\\reducer.js": "50", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\testStatistics.js": "51", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\imageExport.js": "52", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\LearningModule\\reducer.js": "53", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\TextBlock.js": "54", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\FormulaBlock.js": "55", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\ImageBlock.js": "56", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\VideoBlock.js": "57", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\AudioBlock.js": "58", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\MultipleChoiceQuestion.js": "59", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\FillInBlankQuestion.js": "60", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\QuestionRenderer.js": "61", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\TrueFalseQuestion.js": "62", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\SingleChoiceQuestion.js": "63", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\ReadingComprehensionQuestion.js": "64", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\ListeningQuestion.js": "65", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\BlankInput.js": "66", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\OptionSelector.js": "67", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\ResultDisplay.js": "68", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\AnswerSubmitter.js": "69", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\core\\BlankDetector.js": "70", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\core\\AnswerValidator.js": "71", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\core\\MathLiveInputManager.js": "72", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\QuestionTypeDetector.js": "73", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\SmartContentRenderer.js": "74", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\VideoDebugInfo.js": "75", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\core\\CompatibilityAdapter.js": "76", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\NestedQuestionRenderer.js": "77", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\index.js": "78", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\models\\index.js": "79", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\models\\enums.js": "80", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\htmlSanitizer.js": "81", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\dataValidators.js": "82", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\courseStatusUtils.js": "83", "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\test\\MathInputTest.js": "84"}, {"size": 377, "mtime": 1749866308744, "results": "85", "hashOfConfig": "86"}, {"size": 5729, "mtime": 1753483061651, "results": "87", "hashOfConfig": "86"}, {"size": 11869, "mtime": 1752723434361, "results": "88", "hashOfConfig": "86"}, {"size": 15542, "mtime": 1752723487144, "results": "89", "hashOfConfig": "86"}, {"size": 9683, "mtime": 1750760060264, "results": "90", "hashOfConfig": "86"}, {"size": 5732, "mtime": 1752135261348, "results": "91", "hashOfConfig": "86"}, {"size": 4853, "mtime": 1751374886020, "results": "92", "hashOfConfig": "86"}, {"size": 37843, "mtime": 1753433611128, "results": "93", "hashOfConfig": "86"}, {"size": 15273, "mtime": 1751494318535, "results": "94", "hashOfConfig": "86"}, {"size": 42943, "mtime": 1751498378147, "results": "95", "hashOfConfig": "86"}, {"size": 13608, "mtime": 1751872404170, "results": "96", "hashOfConfig": "86"}, {"size": 2176, "mtime": 1751531576111, "results": "97", "hashOfConfig": "86"}, {"size": 17070, "mtime": 1753482870454, "results": "98", "hashOfConfig": "86"}, {"size": 8731, "mtime": 1750836124578, "results": "99", "hashOfConfig": "86"}, {"size": 14388, "mtime": 1753436503675, "results": "100", "hashOfConfig": "86"}, {"size": 19355, "mtime": 1753442373387, "results": "101", "hashOfConfig": "86"}, {"size": 2341, "mtime": 1752131558786, "results": "102", "hashOfConfig": "86"}, {"size": 3858, "mtime": 1752539257134, "results": "103", "hashOfConfig": "86"}, {"size": 6647, "mtime": 1750578602550, "results": "104", "hashOfConfig": "86"}, {"size": 13512, "mtime": 1753413194103, "results": "105", "hashOfConfig": "86"}, {"size": 1558, "mtime": 1750957651011, "results": "106", "hashOfConfig": "86"}, {"size": 824, "mtime": 1752135261380, "results": "107", "hashOfConfig": "86"}, {"size": 15726, "mtime": 1753442284508, "results": "108", "hashOfConfig": "86"}, {"size": 7737, "mtime": 1751494318535, "results": "109", "hashOfConfig": "86"}, {"size": 1225, "mtime": 1753431262368, "results": "110", "hashOfConfig": "86"}, {"size": 10132, "mtime": 1752568607404, "results": "111", "hashOfConfig": "86"}, {"size": 11213, "mtime": 1751487015127, "results": "112", "hashOfConfig": "86"}, {"size": 16957, "mtime": 1751910923572, "results": "113", "hashOfConfig": "86"}, {"size": 48889, "mtime": 1753663612671, "results": "114", "hashOfConfig": "115"}, {"size": 41711, "mtime": 1752544049123, "results": "116", "hashOfConfig": "86"}, {"size": 3273, "mtime": 1751252130662, "results": "117", "hashOfConfig": "86"}, {"size": 108, "mtime": 1751825603283, "results": "118", "hashOfConfig": "86"}, {"size": 21086, "mtime": 1753634199973, "results": "119", "hashOfConfig": "86"}, {"size": 25900, "mtime": 1753482898283, "results": "120", "hashOfConfig": "86"}, {"size": 1042, "mtime": 1750490223615, "results": "121", "hashOfConfig": "86"}, {"size": 1278, "mtime": 1751849908604, "results": "122", "hashOfConfig": "86"}, {"size": 33984, "mtime": 1751914220852, "results": "123", "hashOfConfig": "86"}, {"size": 838, "mtime": 1750182429531, "results": "124", "hashOfConfig": "86"}, {"size": 11324, "mtime": 1752662891378, "results": "125", "hashOfConfig": "86"}, {"size": 555, "mtime": 1750574111538, "results": "126", "hashOfConfig": "86"}, {"size": 1146, "mtime": 1751358199116, "results": "127", "hashOfConfig": "86"}, {"size": 681, "mtime": 1750182429530, "results": "128", "hashOfConfig": "86"}, {"size": 2923, "mtime": 1750182429530, "results": "129", "hashOfConfig": "86"}, {"size": 1000, "mtime": 1750580237477, "results": "130", "hashOfConfig": "86"}, {"size": 26934, "mtime": 1753638202712, "results": "131", "hashOfConfig": "115"}, {"size": 5182, "mtime": 1750420399745, "results": "132", "hashOfConfig": "86"}, {"size": 1108, "mtime": 1751483245754, "results": "133", "hashOfConfig": "86"}, {"size": 6700, "mtime": 1751498155444, "results": "134", "hashOfConfig": "86"}, {"size": 10940, "mtime": 1751825976097, "results": "135", "hashOfConfig": "86"}, {"size": 7513, "mtime": 1753436523907, "results": "136", "hashOfConfig": "86"}, {"size": 7261, "mtime": 1753421899506, "results": "137", "hashOfConfig": "86"}, {"size": 12754, "mtime": 1750908242969, "results": "138", "hashOfConfig": "86"}, {"size": 14302, "mtime": 1753663806101, "results": "139", "hashOfConfig": "115"}, {"size": 6275, "mtime": 1751230546469, "results": "140", "hashOfConfig": "86"}, {"size": 6559, "mtime": 1751871922856, "results": "141", "hashOfConfig": "86"}, {"size": 7217, "mtime": 1751254235989, "results": "142", "hashOfConfig": "86"}, {"size": 19327, "mtime": 1751383359761, "results": "143", "hashOfConfig": "86"}, {"size": 8421, "mtime": 1750370657811, "results": "144", "hashOfConfig": "86"}, {"size": 11623, "mtime": 1753411596037, "results": "145", "hashOfConfig": "86"}, {"size": 19494, "mtime": 1753638202704, "results": "146", "hashOfConfig": "115"}, {"size": 11305, "mtime": 1751579551550, "results": "147", "hashOfConfig": "86"}, {"size": 6808, "mtime": 1751438057780, "results": "148", "hashOfConfig": "86"}, {"size": 10906, "mtime": 1753436777647, "results": "149", "hashOfConfig": "86"}, {"size": 8949, "mtime": 1751438001740, "results": "150", "hashOfConfig": "86"}, {"size": 10810, "mtime": 1751438074944, "results": "151", "hashOfConfig": "86"}, {"size": 9781, "mtime": 1753343579143, "results": "152", "hashOfConfig": "86"}, {"size": 8332, "mtime": 1750378803032, "results": "153", "hashOfConfig": "86"}, {"size": 11240, "mtime": 1751312064216, "results": "154", "hashOfConfig": "86"}, {"size": 11494, "mtime": 1750372564889, "results": "155", "hashOfConfig": "86"}, {"size": 12670, "mtime": 1751323444099, "results": "156", "hashOfConfig": "86"}, {"size": 12082, "mtime": 1751300542591, "results": "157", "hashOfConfig": "86"}, {"size": 15736, "mtime": 1753634199960, "results": "158", "hashOfConfig": "86"}, {"size": 8923, "mtime": 1750420713317, "results": "159", "hashOfConfig": "86"}, {"size": 4829, "mtime": 1751252858923, "results": "160", "hashOfConfig": "86"}, {"size": 3315, "mtime": 1750937445601, "results": "161", "hashOfConfig": "86"}, {"size": 10233, "mtime": 1751849908604, "results": "162", "hashOfConfig": "86"}, {"size": 11487, "mtime": 1751437972319, "results": "163", "hashOfConfig": "86"}, {"size": 3395, "mtime": 1750985455526, "results": "164", "hashOfConfig": "86"}, {"size": 1372, "mtime": 1750393883868, "results": "165", "hashOfConfig": "86"}, {"size": 3765, "mtime": 1750393780118, "results": "166", "hashOfConfig": "86"}, {"size": 4922, "mtime": 1750477561361, "results": "167", "hashOfConfig": "86"}, {"size": 13692, "mtime": 1750394241802, "results": "168", "hashOfConfig": "86"}, {"size": 2714, "mtime": 1752723353742, "results": "169", "hashOfConfig": "86"}, {"size": 6974, "mtime": 1753525439950, "results": "170", "hashOfConfig": "86"}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "nm3aav", {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1hojro7", {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\projects\\AIstrusys\\frontend\\learning\\src\\index.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\App.js", ["423"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\pages\\StudyHome.js", ["424", "425", "426", "427", "428", "429", "430"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\pages\\ClassUnit.js", ["431", "432", "433", "434", "435", "436"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\apiUtils.js", ["437"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\pages\\Login.js", ["438", "439", "440", "441", "442", "443", "444"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\pages\\LearningResult.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\pages\\LearningTrack.js", ["445", "446", "447", "448"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\pages\\WrongQuestionDetail.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\pages\\WrongQuestions.js", ["449", "450", "451", "452", "453", "454", "455", "456", "457", "458", "459", "460", "461", "462"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\pages\\WrongQuestionElimination.js", ["463", "464", "465", "466"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\hooks\\useEyeProtection.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\core\\GlobalKeyboardManager.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\QuestionRedoModule\\index.js", ["467"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\ChapterQuizModule\\index.js", ["468"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\LearningModule\\index.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\auth.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\displayUtils.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\questionFormatValidator.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\HtmlContentRenderer.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\api\\course.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\api\\auth.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\api\\learning.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\api\\wrongQuestions.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\api\\user.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\api\\intelligentReport.js", ["469"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\questionDataProcessor.js", ["470", "471"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\common\\QuestionDetailModal.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\LearningModule\\scenes\\ResultScreen.js", ["472", "473", "474", "475", "476", "477", "478", "479"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\ChapterQuizModule\\scenes\\IntelligentReportModal.js", ["480"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\wrongQuestions\\WrongQuestionDateGroup.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\common\\index.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\LearningModule\\scenes\\QuestionScreen.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\core\\MathQuillKeyboard.js", ["481"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\ChapterQuizModule\\context.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\ChapterQuizModule\\ErrorBoundary.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\ChapterQuizModule\\scenes\\ChapterResultScreen.js", ["482", "483", "484", "485", "486", "487", "488", "489", "490", "491", "492", "493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\LearningModule\\context.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\LearningModule\\scenes\\VideoScreen.js", ["504", "505", "506", "507"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\index.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\api\\axios.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\LearningModule\\common\\LoadingSpinner.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\LearningModule\\common\\ErrorAlert.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\index.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\core\\UnifiedBlankRenderer.js", ["508", "509"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\ContentBlockRenderer.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\common\\BackButton.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\wrongQuestions\\WrongQuestionCard.js", ["510"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\common\\VideoModal.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\ChapterQuizModule\\reducer.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\testStatistics.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\imageExport.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\LearningModule\\reducer.js", ["511"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\TextBlock.js", ["512", "513"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\FormulaBlock.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\ImageBlock.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\VideoBlock.js", ["514", "515"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\AudioBlock.js", ["516", "517"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\MultipleChoiceQuestion.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\FillInBlankQuestion.js", ["518", "519", "520", "521", "522", "523"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\QuestionRenderer.js", ["524", "525", "526", "527"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\TrueFalseQuestion.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\SingleChoiceQuestion.js", ["528"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\ReadingComprehensionQuestion.js", ["529", "530", "531"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\ListeningQuestion.js", ["532", "533", "534", "535", "536"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\BlankInput.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\OptionSelector.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\ResultDisplay.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\AnswerSubmitter.js", ["537", "538", "539"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\core\\BlankDetector.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\core\\AnswerValidator.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\core\\MathLiveInputManager.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\QuestionTypeDetector.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\SmartContentRenderer.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\content\\VideoDebugInfo.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\core\\CompatibilityAdapter.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\questions\\NestedQuestionRenderer.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\index.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\models\\index.js", ["540"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\models\\enums.js", ["541"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\htmlSanitizer.js", ["542", "543"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\dataValidators.js", ["544", "545", "546"], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\utils\\courseStatusUtils.js", [], [], "D:\\projects\\AIstrusys\\frontend\\learning\\src\\components\\test\\MathInputTest.js", ["547", "548"], [], {"ruleId": "549", "severity": 1, "message": "550", "line": 27, "column": 7, "nodeType": "551", "messageId": "552", "endLine": 27, "endColumn": 16}, {"ruleId": "549", "severity": 1, "message": "553", "line": 2, "column": 52, "nodeType": "551", "messageId": "552", "endLine": 2, "endColumn": 57}, {"ruleId": "549", "severity": 1, "message": "554", "line": 2, "column": 68, "nodeType": "551", "messageId": "552", "endLine": 2, "endColumn": 73}, {"ruleId": "549", "severity": 1, "message": "555", "line": 4, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 4, "endColumn": 15}, {"ruleId": "549", "severity": 1, "message": "556", "line": 8, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 8, "endColumn": 20}, {"ruleId": "549", "severity": 1, "message": "557", "line": 9, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 9, "endColumn": 18}, {"ruleId": "549", "severity": 1, "message": "558", "line": 17, "column": 26, "nodeType": "551", "messageId": "552", "endLine": 17, "endColumn": 32}, {"ruleId": "549", "severity": 1, "message": "559", "line": 85, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 85, "endColumn": 23}, {"ruleId": "549", "severity": 1, "message": "560", "line": 6, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 6, "endColumn": 21}, {"ruleId": "549", "severity": 1, "message": "561", "line": 11, "column": 28, "nodeType": "551", "messageId": "552", "endLine": 11, "endColumn": 49}, {"ruleId": "562", "severity": 1, "message": "563", "line": 80, "column": 6, "nodeType": "564", "endLine": 80, "endColumn": 47, "suggestions": "565"}, {"ruleId": "549", "severity": 1, "message": "566", "line": 112, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 112, "endColumn": 33}, {"ruleId": "549", "severity": 1, "message": "567", "line": 147, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 147, "endColumn": 31}, {"ruleId": "549", "severity": 1, "message": "568", "line": 263, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 263, "endColumn": 26}, {"ruleId": "569", "severity": 1, "message": "570", "line": 342, "column": 1, "nodeType": "571", "endLine": 347, "endColumn": 3}, {"ruleId": "549", "severity": 1, "message": "572", "line": 2, "column": 62, "nodeType": "551", "messageId": "552", "endLine": 2, "endColumn": 66}, {"ruleId": "549", "severity": 1, "message": "573", "line": 5, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 5, "endColumn": 28}, {"ruleId": "549", "severity": 1, "message": "574", "line": 8, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 8, "endColumn": 14}, {"ruleId": "549", "severity": 1, "message": "575", "line": 15, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 15, "endColumn": 17}, {"ruleId": "549", "severity": 1, "message": "576", "line": 84, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 84, "endColumn": 24}, {"ruleId": "577", "severity": 1, "message": "578", "line": 166, "column": 13, "nodeType": "579", "endLine": 166, "endColumn": 52}, {"ruleId": "577", "severity": 1, "message": "580", "line": 167, "column": 13, "nodeType": "579", "endLine": 170, "endColumn": 14}, {"ruleId": "549", "severity": 1, "message": "581", "line": 2, "column": 30, "nodeType": "551", "messageId": "552", "endLine": 2, "endColumn": 36}, {"ruleId": "549", "severity": 1, "message": "582", "line": 3, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 3, "endColumn": 27}, {"ruleId": "549", "severity": 1, "message": "583", "line": 351, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 351, "endColumn": 28}, {"ruleId": "562", "severity": 1, "message": "584", "line": 489, "column": 6, "nodeType": "564", "endLine": 489, "endColumn": 25, "suggestions": "585"}, {"ruleId": "549", "severity": 1, "message": "586", "line": 7, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 7, "endColumn": 17}, {"ruleId": "549", "severity": 1, "message": "587", "line": 7, "column": 19, "nodeType": "551", "messageId": "552", "endLine": 7, "endColumn": 33}, {"ruleId": "549", "severity": 1, "message": "588", "line": 8, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 8, "endColumn": 15}, {"ruleId": "549", "severity": 1, "message": "589", "line": 8, "column": 17, "nodeType": "551", "messageId": "552", "endLine": 8, "endColumn": 29}, {"ruleId": "549", "severity": 1, "message": "590", "line": 8, "column": 31, "nodeType": "551", "messageId": "552", "endLine": 8, "endColumn": 43}, {"ruleId": "549", "severity": 1, "message": "591", "line": 11, "column": 29, "nodeType": "551", "messageId": "552", "endLine": 11, "endColumn": 47}, {"ruleId": "549", "severity": 1, "message": "592", "line": 20, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 20, "endColumn": 15}, {"ruleId": "549", "severity": 1, "message": "593", "line": 20, "column": 17, "nodeType": "551", "messageId": "552", "endLine": 20, "endColumn": 24}, {"ruleId": "549", "severity": 1, "message": "594", "line": 21, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 21, "endColumn": 15}, {"ruleId": "549", "severity": 1, "message": "595", "line": 33, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 33, "endColumn": 20}, {"ruleId": "549", "severity": 1, "message": "596", "line": 33, "column": 22, "nodeType": "551", "messageId": "552", "endLine": 33, "endColumn": 35}, {"ruleId": "549", "severity": 1, "message": "597", "line": 34, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 34, "endColumn": 18}, {"ruleId": "549", "severity": 1, "message": "598", "line": 38, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 38, "endColumn": 18}, {"ruleId": "562", "severity": 1, "message": "599", "line": 122, "column": 6, "nodeType": "564", "endLine": 122, "endColumn": 23, "suggestions": "600"}, {"ruleId": "549", "severity": 1, "message": "601", "line": 6, "column": 33, "nodeType": "551", "messageId": "552", "endLine": 6, "endColumn": 52}, {"ruleId": "549", "severity": 1, "message": "602", "line": 12, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 12, "endColumn": 36}, {"ruleId": "549", "severity": 1, "message": "603", "line": 110, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 110, "endColumn": 19}, {"ruleId": "549", "severity": 1, "message": "604", "line": 299, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 299, "endColumn": 19}, {"ruleId": "549", "severity": 1, "message": "605", "line": 3, "column": 39, "nodeType": "551", "messageId": "552", "endLine": 3, "endColumn": 43}, {"ruleId": "562", "severity": 1, "message": "606", "line": 348, "column": 6, "nodeType": "564", "endLine": 362, "endColumn": 4, "suggestions": "607"}, {"ruleId": "608", "severity": 1, "message": "609", "line": 265, "column": 5, "nodeType": "610", "messageId": "611", "endLine": 278, "endColumn": 6}, {"ruleId": "549", "severity": 1, "message": "612", "line": 31, "column": 11, "nodeType": "551", "messageId": "552", "endLine": 31, "endColumn": 22}, {"ruleId": "613", "severity": 1, "message": "614", "line": 387, "column": 3, "nodeType": "615", "messageId": "616", "endLine": 400, "endColumn": 4}, {"ruleId": "549", "severity": 1, "message": "617", "line": 2, "column": 37, "nodeType": "551", "messageId": "552", "endLine": 2, "endColumn": 40}, {"ruleId": "549", "severity": 1, "message": "601", "line": 3, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 3, "endColumn": 29}, {"ruleId": "549", "severity": 1, "message": "618", "line": 3, "column": 31, "nodeType": "551", "messageId": "552", "endLine": 3, "endColumn": 44}, {"ruleId": "549", "severity": 1, "message": "619", "line": 3, "column": 46, "nodeType": "551", "messageId": "552", "endLine": 3, "endColumn": 65}, {"ruleId": "549", "severity": 1, "message": "620", "line": 3, "column": 67, "nodeType": "551", "messageId": "552", "endLine": 3, "endColumn": 92}, {"ruleId": "549", "severity": 1, "message": "621", "line": 9, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 9, "endColumn": 26}, {"ruleId": "562", "severity": 1, "message": "622", "line": 203, "column": 6, "nodeType": "564", "endLine": 203, "endColumn": 81, "suggestions": "623"}, {"ruleId": "549", "severity": 1, "message": "583", "line": 459, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 459, "endColumn": 28}, {"ruleId": "549", "severity": 1, "message": "624", "line": 80, "column": 11, "nodeType": "551", "messageId": "552", "endLine": 80, "endColumn": 19}, {"ruleId": "613", "severity": 1, "message": "614", "line": 529, "column": 11, "nodeType": "615", "messageId": "616", "endLine": 542, "endColumn": 12}, {"ruleId": "549", "severity": 1, "message": "605", "line": 3, "column": 30, "nodeType": "551", "messageId": "552", "endLine": 3, "endColumn": 34}, {"ruleId": "549", "severity": 1, "message": "625", "line": 3, "column": 46, "nodeType": "551", "messageId": "552", "endLine": 3, "endColumn": 49}, {"ruleId": "549", "severity": 1, "message": "626", "line": 3, "column": 51, "nodeType": "551", "messageId": "552", "endLine": 3, "endColumn": 54}, {"ruleId": "549", "severity": 1, "message": "617", "line": 3, "column": 65, "nodeType": "551", "messageId": "552", "endLine": 3, "endColumn": 68}, {"ruleId": "549", "severity": 1, "message": "627", "line": 3, "column": 70, "nodeType": "551", "messageId": "552", "endLine": 3, "endColumn": 74}, {"ruleId": "549", "severity": 1, "message": "628", "line": 3, "column": 76, "nodeType": "551", "messageId": "552", "endLine": 3, "endColumn": 81}, {"ruleId": "549", "severity": 1, "message": "629", "line": 10, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 10, "endColumn": 19}, {"ruleId": "549", "severity": 1, "message": "630", "line": 11, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 11, "endColumn": 14}, {"ruleId": "549", "severity": 1, "message": "620", "line": 13, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 13, "endColumn": 28}, {"ruleId": "549", "severity": 1, "message": "574", "line": 23, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 23, "endColumn": 14}, {"ruleId": "549", "severity": 1, "message": "631", "line": 23, "column": 16, "nodeType": "551", "messageId": "552", "endLine": 23, "endColumn": 20}, {"ruleId": "549", "severity": 1, "message": "632", "line": 40, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 40, "endColumn": 19}, {"ruleId": "562", "severity": 1, "message": "622", "line": 207, "column": 6, "nodeType": "564", "endLine": 207, "endColumn": 91, "suggestions": "633"}, {"ruleId": "549", "severity": 1, "message": "634", "line": 210, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 210, "endColumn": 14}, {"ruleId": "549", "severity": 1, "message": "635", "line": 232, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 232, "endColumn": 33}, {"ruleId": "562", "severity": 1, "message": "636", "line": 293, "column": 6, "nodeType": "564", "endLine": 293, "endColumn": 24, "suggestions": "637"}, {"ruleId": "562", "severity": 1, "message": "638", "line": 309, "column": 6, "nodeType": "564", "endLine": 309, "endColumn": 94, "suggestions": "639"}, {"ruleId": "562", "severity": 1, "message": "640", "line": 379, "column": 6, "nodeType": "564", "endLine": 379, "endColumn": 71, "suggestions": "641"}, {"ruleId": "562", "severity": 1, "message": "642", "line": 547, "column": 6, "nodeType": "564", "endLine": 547, "endColumn": 8, "suggestions": "643"}, {"ruleId": "549", "severity": 1, "message": "583", "line": 595, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 595, "endColumn": 28}, {"ruleId": "549", "severity": 1, "message": "644", "line": 774, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 774, "endColumn": 26}, {"ruleId": "549", "severity": 1, "message": "645", "line": 798, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 798, "endColumn": 28}, {"ruleId": "549", "severity": 1, "message": "574", "line": 13, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 13, "endColumn": 14}, {"ruleId": "549", "severity": 1, "message": "646", "line": 34, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 34, "endColumn": 16}, {"ruleId": "549", "severity": 1, "message": "647", "line": 35, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 35, "endColumn": 18}, {"ruleId": "549", "severity": 1, "message": "648", "line": 97, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 97, "endColumn": 19}, {"ruleId": "562", "severity": 1, "message": "649", "line": 655, "column": 25, "nodeType": "551", "endLine": 655, "endColumn": 32}, {"ruleId": "562", "severity": 1, "message": "650", "line": 657, "column": 6, "nodeType": "564", "endLine": 657, "endColumn": 80, "suggestions": "651"}, {"ruleId": "549", "severity": 1, "message": "652", "line": 7, "column": 33, "nodeType": "551", "messageId": "552", "endLine": 7, "endColumn": 40}, {"ruleId": "653", "severity": 1, "message": "654", "line": 410, "column": 5, "nodeType": "655", "messageId": "656", "endLine": 414, "endColumn": 9}, {"ruleId": "549", "severity": 1, "message": "657", "line": 205, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 205, "endColumn": 20}, {"ruleId": "658", "severity": 1, "message": "659", "line": 217, "column": 74, "nodeType": "660", "messageId": "661", "endLine": 217, "endColumn": 87}, {"ruleId": "549", "severity": 1, "message": "662", "line": 61, "column": 5, "nodeType": "551", "messageId": "552", "endLine": 61, "endColumn": 13}, {"ruleId": "562", "severity": 1, "message": "663", "line": 225, "column": 6, "nodeType": "564", "endLine": 225, "endColumn": 45, "suggestions": "664"}, {"ruleId": "549", "severity": 1, "message": "662", "line": 46, "column": 5, "nodeType": "551", "messageId": "552", "endLine": 46, "endColumn": 13}, {"ruleId": "549", "severity": 1, "message": "665", "line": 48, "column": 5, "nodeType": "551", "messageId": "552", "endLine": 48, "endColumn": 13}, {"ruleId": "549", "severity": 1, "message": "666", "line": 7, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 7, "endColumn": 15}, {"ruleId": "549", "severity": 1, "message": "601", "line": 8, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 8, "endColumn": 29}, {"ruleId": "549", "severity": 1, "message": "619", "line": 8, "column": 31, "nodeType": "551", "messageId": "552", "endLine": 8, "endColumn": 50}, {"ruleId": "549", "severity": 1, "message": "667", "line": 14, "column": 8, "nodeType": "551", "messageId": "552", "endLine": 14, "endColumn": 28}, {"ruleId": "549", "severity": 1, "message": "668", "line": 153, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 153, "endColumn": 30}, {"ruleId": "549", "severity": 1, "message": "669", "line": 257, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 257, "endColumn": 28}, {"ruleId": "549", "severity": 1, "message": "670", "line": 11, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 11, "endColumn": 23}, {"ruleId": "549", "severity": 1, "message": "671", "line": 12, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 12, "endColumn": 26}, {"ruleId": "549", "severity": 1, "message": "672", "line": 18, "column": 8, "nodeType": "551", "messageId": "552", "endLine": 18, "endColumn": 36}, {"ruleId": "549", "severity": 1, "message": "673", "line": 19, "column": 8, "nodeType": "551", "messageId": "552", "endLine": 19, "endColumn": 25}, {"ruleId": "549", "severity": 1, "message": "674", "line": 12, "column": 8, "nodeType": "551", "messageId": "552", "endLine": 12, "endColumn": 22}, {"ruleId": "549", "severity": 1, "message": "601", "line": 8, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 8, "endColumn": 29}, {"ruleId": "549", "severity": 1, "message": "619", "line": 8, "column": 31, "nodeType": "551", "messageId": "552", "endLine": 8, "endColumn": 50}, {"ruleId": "549", "severity": 1, "message": "675", "line": 198, "column": 23, "nodeType": "551", "messageId": "552", "endLine": 198, "endColumn": 32}, {"ruleId": "549", "severity": 1, "message": "560", "line": 8, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 8, "endColumn": 28}, {"ruleId": "549", "severity": 1, "message": "676", "line": 8, "column": 30, "nodeType": "551", "messageId": "552", "endLine": 8, "endColumn": 49}, {"ruleId": "549", "severity": 1, "message": "601", "line": 8, "column": 51, "nodeType": "551", "messageId": "552", "endLine": 8, "endColumn": 70}, {"ruleId": "549", "severity": 1, "message": "619", "line": 8, "column": 72, "nodeType": "551", "messageId": "552", "endLine": 8, "endColumn": 91}, {"ruleId": "549", "severity": 1, "message": "677", "line": 124, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 124, "endColumn": 25}, {"ruleId": "549", "severity": 1, "message": "678", "line": 15, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 15, "endColumn": 30}, {"ruleId": "549", "severity": 1, "message": "679", "line": 15, "column": 32, "nodeType": "551", "messageId": "552", "endLine": 15, "endColumn": 46}, {"ruleId": "549", "severity": 1, "message": "680", "line": 16, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 16, "endColumn": 22}, {"ruleId": "569", "severity": 1, "message": "570", "line": 59, "column": 1, "nodeType": "571", "endLine": 78, "endColumn": 3}, {"ruleId": "569", "severity": 1, "message": "570", "line": 159, "column": 1, "nodeType": "571", "endLine": 173, "endColumn": 3}, {"ruleId": "549", "severity": 1, "message": "681", "line": 9, "column": 7, "nodeType": "551", "messageId": "552", "endLine": 9, "endColumn": 19}, {"ruleId": "549", "severity": 1, "message": "682", "line": 23, "column": 7, "nodeType": "551", "messageId": "552", "endLine": 23, "endColumn": 25}, {"ruleId": "613", "severity": 1, "message": "614", "line": 157, "column": 5, "nodeType": "615", "messageId": "616", "endLine": 200, "endColumn": 6}, {"ruleId": "613", "severity": 1, "message": "614", "line": 242, "column": 5, "nodeType": "615", "messageId": "616", "endLine": 290, "endColumn": 6}, {"ruleId": "569", "severity": 1, "message": "570", "line": 552, "column": 1, "nodeType": "571", "endLine": 562, "endColumn": 3}, {"ruleId": "562", "severity": 1, "message": "683", "line": 52, "column": 28, "nodeType": "551", "endLine": 52, "endColumn": 35}, {"ruleId": "562", "severity": 1, "message": "684", "line": 54, "column": 6, "nodeType": "564", "endLine": 54, "endColumn": 8, "suggestions": "685"}, "no-unused-vars", "'antdTheme' is assigned a value but never used.", "Identifier", "unusedVar", "'Space' is defined but never used.", "'Badge' is defined but never used.", "'UserOutlined' is defined but never used.", "'LineChartOutlined' is defined but never used.", "'HistoryOutlined' is defined but never used.", "'Footer' is assigned a value but never used.", "'getSubjectName' is assigned a value but never used.", "'PlayCircleOutlined' is defined but never used.", "'updateStudentProgress' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCourseDetails'. Either include it or remove the dependency array.", "ArrayExpression", ["686"], "'calculateOverallProgress' is assigned a value but never used.", "'getOverallProgressText' is assigned a value but never used.", "'getProgressStatus' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'Tabs' is defined but never used.", "'handleLoginSuccess' is defined but never used.", "'Title' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'handleTabChange' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'Button' is defined but never used.", "'ArrowLeftOutlined' is defined but never used.", "'renderCorrectAnswer' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'generateMockData'. Either include it or remove the dependency array.", ["687"], "'ReloadOutlined' is defined but never used.", "'DeleteOutlined' is defined but never used.", "'BookOutlined' is defined but never used.", "'LeftOutlined' is defined but never used.", "'BulbOutlined' is defined but never used.", "'TIME_RANGE_OPTIONS' is defined but never used.", "'Header' is assigned a value but never used.", "'Content' is assigned a value but never used.", "'Option' is assigned a value but never used.", "'statistics' is assigned a value but never used.", "'setStatistics' is assigned a value but never used.", "'subjects' is assigned a value but never used.", "'isMobile' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'PREDEFINED_SUBJECTS'. Either include it or remove the dependency array.", ["688"], "'CheckCircleOutlined' is defined but never used.", "'processBackendQuestionData' is defined but never used.", "'gameStats' is assigned a value but never used.", "'handleNext' is assigned a value but never used.", "'Card' is defined but never used.", "React Hook useMemo has missing dependencies: 'dispatch' and 'state'. Either include them or remove the dependency array.", ["689"], "no-unreachable", "Unreachable code.", "IfStatement", "unreachableCode", "'displayName' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'Tag' is defined but never used.", "'RightOutlined' is defined but never used.", "'CloseCircleOutlined' is defined but never used.", "'ExclamationCircleOutlined' is defined but never used.", "'ContentBlockList' is defined but never used.", "React Hook useEffect has missing dependencies: 'currentSessionId', 'testEndTime', and 'testStartTime'. Either include them or remove the dependency array.", ["690"], "'userName' is assigned a value but never used.", "'Row' is defined but never used.", "'Col' is defined but never used.", "'List' is defined but never used.", "'Modal' is defined but never used.", "'DownloadOutlined' is defined but never used.", "'EyeOutlined' is defined but never used.", "'Text' is assigned a value but never used.", "'exporting' is assigned a value but never used.", ["691"], "'grade' is assigned a value but never used.", "'handleViewWrongQuestions' is assigned a value but never used.", "React Hook useCallback has missing dependencies: 'effectiveQuestions?.length', 'practiceResultSaved', 'testEndTime', and 'testStartTime'. Either include them or remove the dependency array.", ["692"], "React Hook useEffect has a missing dependency: 'handleOpenIntelligentReport'. Either include it or remove the dependency array.", ["693"], "React Hook useCallback has a missing dependency: 'dbData.questions'. Either include it or remove the dependency array.", ["694"], "React Hook useCallback has a missing dependency: 'renderFillInBlankWithAnswers'. Either include it or remove the dependency array.", ["695"], "'handleExportImage' is assigned a value but never used.", "'handlePreviewReport' is assigned a value but never used.", "'played' is assigned a value but never used.", "'duration' is assigned a value but never used.", "'formatTime' is assigned a value but never used.", "The ref value 'inputInstancesRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'inputInstancesRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "React Hook useEffect has a missing dependency: 'blanks'. Either include it or remove the dependency array.", ["696"], "'Divider' is defined but never used.", "no-duplicate-case", "Duplicate case label.", "SwitchCase", "unexpected", "'allowedTags' is assigned a value but never used.", "no-script-url", "Script URL is a form of eval.", "Literal", "unexpectedScriptURL", "'autoplay' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'onEnded', 'onTimeUpdate', 'playbackRate', and 'startTime'. Either include them or remove the dependency array. If 'onTimeUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["697"], "'controls' is assigned a value but never used.", "'Input' is defined but never used.", "'CompatibilityAdapter' is defined but never used.", "'shouldUseMathKeyboard' is assigned a value but never used.", "'getBlankDisplayName' is assigned a value but never used.", "'parseBackendQuestion' is defined but never used.", "'generateQuestionSummary' is defined but never used.", "'ReadingComprehensionQuestion' is defined but never used.", "'ListeningQuestion' is defined but never used.", "'OptionSelector' is defined but never used.", "'subAnswer' is assigned a value but never used.", "'PauseCircleOutlined' is defined but never used.", "'handleAudioEnded' is assigned a value but never used.", "'validateAnswerFormat' is defined but never used.", "'calculateScore' is defined but never used.", "'QuestionType' is defined but never used.", "'ALLOWED_TAGS' is assigned a value but never used.", "'ALLOWED_ATTRIBUTES' is assigned a value but never used.", "The ref value 'mathLiveInputManager.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'mathLiveInputManager.current' to a variable inside the effect, and use that variable in the cleanup function.", "React Hook useEffect has a missing dependency: 'inputValues'. Either include it or remove the dependency array.", ["698"], {"desc": "699", "fix": "700"}, {"desc": "701", "fix": "702"}, {"desc": "703", "fix": "704"}, {"desc": "705", "fix": "706"}, {"desc": "707", "fix": "708"}, {"desc": "709", "fix": "710"}, {"desc": "711", "fix": "712"}, {"desc": "713", "fix": "714"}, {"desc": "715", "fix": "716"}, {"desc": "717", "fix": "718"}, {"desc": "719", "fix": "720"}, {"desc": "721", "fix": "722"}, {"desc": "723", "fix": "724"}, "Update the dependencies array to be: [studentId, subjectVersionId, refreshKey, fetchCourseDetails]", {"range": "725", "text": "726"}, "Update the dependencies array to be: [generateMockData, selectedTimeRange]", {"range": "727", "text": "728"}, "Update the dependencies array to be: [PREDEFINED_SUBJECTS, selectedSubject]", {"range": "729", "text": "730"}, "Update the dependencies array to be: [state, currentQuestion, handleAnswerSubmit, handleAnswerSelect, dispatch, handleRestart, handleBack]", {"range": "731", "text": "732"}, "Update the dependencies array to be: [knowledgePoint.id, validQuestions, validUserAnswers, practiceResultSaved, testStartTime, testEndTime, currentSessionId]", {"range": "733", "text": "734"}, "Update the dependencies array to be: [currentSessionId, effectiveChapter?.id, effectiveQuestions, effectiveUserAnswers, practiceResultSaved, testEndTime, testStartTime]", {"range": "735", "text": "736"}, "Update the dependencies array to be: [currentSessionId, effectiveQuestions?.length, practiceResultSaved, testEndTime, testStartTime]", {"range": "737", "text": "738"}, "Update the dependencies array to be: [currentSessionId, practiceResultSaved, intelligentReportData, intelligentReportLoading, handleOpenIntelligentReport]", {"range": "739", "text": "740"}, "Update the dependencies array to be: [dbData.questions, effectiveQuestions, effectiveUserAnswers, intelligentReportData.questionDetails]", {"range": "741", "text": "742"}, "Update the dependencies array to be: [renderFillInBlankWithAnswers]", {"range": "743", "text": "744"}, "Update the dependencies array to be: [processedHtml, blankList.length, createInputAtPlaceholder, fixSqrtFormat, blanks]", {"range": "745", "text": "746"}, "Update the dependencies array to be: [value, volume, isMuted, loop, onError, startTime, playbackRate, onTimeUpdate, onEnded]", {"range": "747", "text": "748"}, "Update the dependencies array to be: [inputValues]", {"range": "749", "text": "750"}, [2528, 2569], "[studentId, subjectVersionId, refreshKey, fetchCourseDetails]", [18132, 18151], "[generateMockData, selectedTimeRange]", [3738, 3755], "[PREDEFINED_SUBJECTS, selectedSubject]", [10788, 11087], "[state, currentQuestion, handleAnswerSubmit, handleAnswerSelect, dispatch, handleRestart, handleBack]", [7890, 7965], "[knowledgePoint.id, validQuestions, validUserAnswers, practiceResultSaved, testStartTime, testEndTime, currentSessionId]", [7442, 7527], "[currentSessionId, effectiveChapter?.id, effectiveQuestions, effectiveUserAnswers, practiceResultSaved, testEndTime, testStartTime]", [10055, 10073], "[currentSessionId, effectiveQuestions?.length, practiceResultSaved, testEndTime, testStartTime]", [10479, 10567], "[currentSessionId, practiceResultSaved, intelligentReportData, intelligentReportLoading, handleOpenIntelligentReport]", [12728, 12793], "[dbData.questions, effectiveQuestions, effectiveUserAnswers, intelligentReportData.questionDetails]", [19133, 19135], "[renderFillInBlankWithAnswers]", [19995, 20069], "[processedHtml, blankList.length, createInputAtPlaceholder, fixSqrtFormat, blanks]", [6112, 6151], "[value, volume, isMuted, loop, onError, startTime, playbackRate, onTimeUpdate, onEnded]", [1513, 1515], "[inputValues]"]