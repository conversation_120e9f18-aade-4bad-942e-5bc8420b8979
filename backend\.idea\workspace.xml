<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="5c7637c7-dd57-4b91-90ae-a865c1164861" name="Changes" comment="1.0.1" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings">
    <execution />
  </component>
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="$PROJECT_DIR$">
          <activation />
        </task>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="backend" type="f1a62948:ProjectNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="backend" type="f1a62948:ProjectNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="backend" type="f1a62948:ProjectNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
                <item name="build" type="c8890929:TasksNode$1" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="PHP" />
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2yBKol2A7r7xVCCm0UhzI5i4a6W" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Gradle.Build backend.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.backend [bootJar].executor&quot;: &quot;Run&quot;,
    &quot;Gradle.backend [clean].executor&quot;: &quot;Run&quot;,
    &quot;Gradle.backend [jar].executor&quot;: &quot;Run&quot;,
    &quot;Gradle.backend [war].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.BackendApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/projects/AIstrusys/backend&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;SDKs&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Spring Boot.BackendApplication">
    <configuration name="backend [bootJar]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="bootJar" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="backend [clean]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="clean" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="backend [jar]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="jar" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="backend [war]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="war" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="BackendApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="backend.main" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.backend.BackendApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Gradle.backend [bootJar]" />
        <item itemvalue="Gradle.backend [clean]" />
        <item itemvalue="Gradle.backend [jar]" />
        <item itemvalue="Gradle.backend [war]" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-b114ca120d71-intellij.indexing.shared.core-IU-242.21829.142" />
        <option value="bundled-js-predefined-d6986cc7102b-7c0b70fcd90d-JavaScript-IU-242.21829.142" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="5c7637c7-dd57-4b91-90ae-a865c1164861" name="Changes" comment="" />
      <created>1749302706406</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749302706406</updated>
      <workItem from="1749302707507" duration="48441000" />
      <workItem from="1749548349409" duration="22247000" />
      <workItem from="1749684619358" duration="4631000" />
      <workItem from="1749722648199" duration="140583000" />
      <workItem from="1750210537767" duration="4479000" />
      <workItem from="1750275890320" duration="34690000" />
      <workItem from="1750351705327" duration="17521000" />
      <workItem from="1750407652355" duration="59044000" />
      <workItem from="1750768307380" duration="597000" />
      <workItem from="1750818153059" duration="29284000" />
      <workItem from="1750998285515" duration="280000" />
      <workItem from="1750999110245" duration="21000" />
      <workItem from="1751037059011" duration="1691000" />
      <workItem from="1751218326557" duration="43863000" />
      <workItem from="1751545237462" duration="2178000" />
      <workItem from="1751602328591" duration="2570000" />
      <workItem from="1751652892606" duration="265000" />
      <workItem from="1751820763235" duration="7246000" />
      <workItem from="1751872573001" duration="3288000" />
      <workItem from="1752021656373" duration="616000" />
      <workItem from="1752134367510" duration="2626000" />
      <workItem from="1752546567739" duration="5177000" />
      <workItem from="1752719482427" duration="12245000" />
      <workItem from="1753285754344" duration="18000" />
      <workItem from="1753406732271" duration="9855000" />
      <workItem from="1753661945562" duration="569000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>