/**
 * 填空题组件
 */

import React from 'react';
import PropTypes from 'prop-types';
import { Input, Alert } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { MathJax } from 'better-react-mathjax';
import { ContentBlockList } from '../content/ContentBlockRenderer';
import HtmlContentRenderer from '../content/HtmlContentRenderer';
// 新的统一渲染器
import UnifiedBlankRenderer from '../core/UnifiedBlankRenderer';
import CompatibilityAdapter from '../core/CompatibilityAdapter';
import QuestionTypeDetector from '../../utils/QuestionTypeDetector';
import BlankInput from './BlankInput';
// 移除不存在的QuestionContext导入
// import { QuestionContext } from './QuestionRenderer';
// 导入新的样式
import '../core/UnifiedBlankRenderer.css';



/**
 * 填空题组件
 * 
 * @param {Object} props 组件属性
 * @param {Object} props.questionData 题目数据
 * @param {Object} props.userAnswer 用户答案（对象，键为空格ID，值为答案）
 * @param {Function} props.onAnswerChange 答案变化回调
 * @param {boolean} props.disabled 是否禁用
 * @param {boolean} props.showResult 是否显示结果
 * @param {Object} props.result 答题结果
 * @param {string} props.className CSS类名
 * @param {Object} props.style 样式对象
 * @param {Function} props.onError 错误处理回调
 * @returns {JSX.Element} 填空题组件
 */
const FillInBlankQuestion = ({
  questionData,
  userAnswer = {},
  onAnswerChange = null,
  disabled = false,
  showResult = false,
  result = null,
  className = '',
  style = {},
  onError = null
}) => {
  // 获取科目信息
  const getSubject = () => {
    // 从题目数据中获取科目信息
    if (questionData?.subject) {
      return questionData.subject.toUpperCase();
    }

    // 从题目数据的其他字段尝试获取科目信息
    if (questionData?.subjectName) {
      return questionData.subjectName.toUpperCase();
    }

    // 默认返回数学
    return 'MATH';
  };

  const subject = getSubject();

  // 检测HTML内容是否包含下划线
  const hasUnderlines = (htmlContent) => {
    if (typeof htmlContent !== 'string') return false;

    // 检测各种下划线模式
    const underlinePatterns = [
      /_{3,}/,                    // 连续3个或更多下划线
      /<u>\s*<\/u>/,             // 空的<u>标签
      /<u>\s*_{1,}\s*<\/u>/,     // <u>标签包含的下划线
      /\b_{2,}\b/,               // 单词边界的下划线
      /\[___\]/,                 // 方括号下划线
      /\(___\)/                  // 圆括号下划线
    ];

    return underlinePatterns.some(pattern => pattern.test(htmlContent));
  };

  // 验证题目数据
  if (!questionData) {
    const error = '填空题数据为空';
    if (onError) onError(error);
    return (
      <Alert
        message="题目错误"
        description={error}
        type="error"
        showIcon
        className={className}
        style={style}
      />
    );
  }

  // 判断数据格式
  const isHtmlFormat = QuestionTypeDetector.isHtmlFormat(questionData);

  // 获取题目内容
  const getQuestionContent = () => {
    if (isHtmlFormat) {
      return questionData.content || '';
    } else {
      return questionData.content || [];
    }
  };

  const content = getQuestionContent();
  const { answer } = questionData;

  // 调试：检查题目内容和下划线检测
  console.log('FillInBlankQuestion - 题目数据:', questionData);
  console.log('FillInBlankQuestion - 题目内容:', content);
  console.log('FillInBlankQuestion - 是否HTML格式:', isHtmlFormat);
  if (isHtmlFormat) {
    console.log('FillInBlankQuestion - 是否包含下划线:', hasUnderlines(content));
    // 测试每个下划线模式
    const underlinePatterns = [
      { name: '连续3个或更多下划线', regex: /_{3,}/ },
      { name: '空的u标签', regex: /<u>\s*<\/u>/ },
      { name: 'u标签包含下划线', regex: /<u>\s*_{1,}\s*<\/u>/ },
      { name: '单词边界下划线', regex: /\b_{2,}\b/ },
      { name: '方括号下划线', regex: /\[___\]/ },
      { name: '圆括号下划线', regex: /\(___\)/ }
    ];
    underlinePatterns.forEach(pattern => {
      const matches = pattern.regex.test(content);
      console.log(`FillInBlankQuestion - ${pattern.name}:`, matches);
    });
  }

  // 确保用户答案是对象
  const currentAnswer = typeof userAnswer === 'object' && userAnswer !== null ? userAnswer : {};

  // 处理单个空格答案变化
  const handleBlankChange = (blankId, value) => {
    if (disabled || !onAnswerChange) return;

    const newAnswer = {
      ...currentAnswer,
      [blankId]: value
    };

    onAnswerChange(newAnswer);
  };

  // 判断是否使用数学键盘
  const shouldUseMathKeyboard = () => {
    const mathSubjects = ['MATH', 'PHYSICS', 'CHEMISTRY'];
    return mathSubjects.includes(subject?.toUpperCase());
  };

  // 渲染填空输入框（现在统一使用BlankInput，数学功能由UnifiedBlankRenderer处理）
  const renderBlankInput = (blankId, value, onChange, additionalProps = {}) => {
    return (
      <BlankInput
        blankId={blankId}
        value={value}
        onChange={onChange}
        disabled={disabled}
        showResult={showResult}
        {...additionalProps}
      />
    );
  };

  // 获取正确答案
  const getCorrectAnswers = () => {
    // HTML格式：answer是数组
    if (Array.isArray(answer)) {
      const result = {};
      answer.forEach((ans, index) => {
        result[`blank_${index}`] = ans; // 使用从0开始的索引，与UnderlineRenderer保持一致
      });
      return result;
    }

    // ContentBlock格式：answer.value是对象
    if (answer && typeof answer.value === 'object') {
      return answer.value;
    }

    // 如果answer直接是对象
    if (answer && typeof answer === 'object' && !Array.isArray(answer)) {
      return answer;
    }

    return {};
  };

  // 修复LaTeX根号格式
  const fixSqrtFormat = (latex) => {
    if (!latex || typeof latex !== 'string') return latex;

    let processed = latex;

    // 🔧 修复错误的n次根格式：\sqrt[6]{5} -> 5\sqrt{6}
    // 这种格式通常是用户输入"5根号6"被错误解析造成的
    processed = processed.replace(/\\sqrt\[(\d+)\]\{(\d+)\}/g, (match, index, content) => {
      console.log('🔧 修复错误的根号格式:', match, '→', `${content}\\sqrt{${index}}`);
      return `${content}\\sqrt{${index}}`;
    });

    // 修复其他可能的错误格式
    processed = processed.replace(/\\sqrt\[([^\]]+)\]\{([^}]+)\}/g, (match, index, content) => {
      // 如果索引和内容都是简单的数字或字母，可能是错误格式
      if (/^[a-zA-Z0-9]+$/.test(index) && /^[a-zA-Z0-9]+$/.test(content)) {
        console.log('🔧 修复复杂根号格式:', match, '→', `${content}\\sqrt{${index}}`);
        return `${content}\\sqrt{${index}}`;
      }
      return match; // 保持原样
    });

    return processed;
  };

  // 渲染答案内容，支持HTML和数学公式
  const renderAnswerContent = (content) => {
    if (!content) return '';

    const contentStr = String(content);

    // 检查是否包含HTML标签
    if (contentStr.includes('<')) {
      return (
        <HtmlContentRenderer
          htmlContent={contentStr}
          onError={onError}
        />
      );
    }

    // 检查是否包含LaTeX数学公式
    if (contentStr.includes('\\') || contentStr.includes('{') || contentStr.includes('^') || contentStr.includes('_')) {
      // 🔧 在渲染前修复LaTeX格式
      const fixedLatex = fixSqrtFormat(contentStr);

      return (
        <div style={{ textAlign: 'left' }}>
          <MathJax>
            {`$${fixedLatex}$`}
          </MathJax>
        </div>
      );
    }

    // 普通文本
    return contentStr;
  };

  // 将blankId转换为友好的显示格式
  const getBlankDisplayName = (blankId) => {
    if (blankId.startsWith('blank_')) {
      const index = parseInt(blankId.replace('blank_', ''));
      return `第${index + 1}个空`;
    }
    return blankId;
  };



  // 获取空格状态
  const getBlankStatus = (blankId) => {
    if (!showResult || !result) return 'normal';

    // 🔧 优先使用后端返回的详细空格验证结果
    if (result.blankResults && result.blankResults[blankId]) {
      const blankResult = result.blankResults[blankId];
      return blankResult.status; // 直接使用后端返回的状态：'correct', 'incorrect', 'empty'
    }

    // 🔧 降级处理：使用后端返回的整体验证结果
    if (result.isCorrect !== undefined) {
      // 检查用户是否填写了这个空格
      let userBlankAnswer = currentAnswer[blankId];

      // 如果currentAnswer中没有找到用户答案，尝试从result中获取
      if (!userBlankAnswer && result.submittedAnswer) {
        try {
          const submittedAnswer = typeof result.submittedAnswer === 'string'
            ? JSON.parse(result.submittedAnswer)
            : result.submittedAnswer;

          if (submittedAnswer && typeof submittedAnswer === 'object') {
            userBlankAnswer = submittedAnswer[blankId];
          }
        } catch (error) {
          console.warn('解析submittedAnswer失败:', error);
        }
      }

      // 如果用户没有填写这个空格，返回empty
      if (!userBlankAnswer || !String(userBlankAnswer).trim()) {
        return 'empty';
      }

      // 使用后端的整体验证结果
      // 如果整体正确，则所有填写的空格都显示为正确
      // 如果整体错误，则所有填写的空格都显示为错误
      return result.isCorrect ? 'correct' : 'incorrect';
    }

    // 🔧 降级处理：如果没有后端验证结果，使用前端比较（兼容性）
    const correctAnswers = getCorrectAnswers();
    let userBlankAnswer = currentAnswer[blankId];
    let correctBlankAnswer = correctAnswers[blankId];

    // 如果currentAnswer中没有找到用户答案，尝试从result中获取
    if (!userBlankAnswer && result.submittedAnswer) {
      try {
        const submittedAnswer = typeof result.submittedAnswer === 'string'
          ? JSON.parse(result.submittedAnswer)
          : result.submittedAnswer;

        if (submittedAnswer && typeof submittedAnswer === 'object') {
          userBlankAnswer = submittedAnswer[blankId];
        }
      } catch (error) {
        console.warn('解析submittedAnswer失败:', error);
      }
    }

    // 如果没有找到对应的正确答案，尝试其他方式
    if (!correctBlankAnswer && result.correctAnswers) {
      correctBlankAnswer = result.correctAnswers[blankId];
    }

    if (!userBlankAnswer || !String(userBlankAnswer).trim()) return 'empty';

    // 数学公式的智能比较
    const normalizedUser = String(userBlankAnswer).trim().toLowerCase();
    const normalizedCorrect = String(correctBlankAnswer || '').trim().toLowerCase();

    if (normalizedUser === normalizedCorrect) {
      return 'correct';
    }

    // 处理数学表达式的等价形式
    const mathEquivalents = [
      [/\s+/g, ''], // 移除所有空格
      [/\*\*/g, '^'], // ** 转换为 ^
      [/\^(\d+)/g, '^{$1}'], // 添加花括号
      [/sqrt\(/g, '\\sqrt{'], // sqrt() 转换为 \sqrt{}
    ];

    let processedUser = normalizedUser;
    let processedCorrect = normalizedCorrect;

    mathEquivalents.forEach(([pattern, replacement]) => {
      processedUser = processedUser.replace(pattern, replacement);
      processedCorrect = processedCorrect.replace(pattern, replacement);
    });

    return processedUser === processedCorrect ? 'correct' : 'incorrect';
  };

  // 渲染内容块，处理填空占位符
  const renderContentWithBlanks = () => {
    return content.map((block, blockIndex) => {
      if (block.type === 'TEXT' && block.value) {
        // 查找填空占位符 ___1___, ___2___ 等
        const blankPattern = /___(\w+)___/g;
        const textParts = [];
        let lastIndex = 0;
        let match;

        while ((match = blankPattern.exec(block.value)) !== null) {
          const blankId = match[1];
          const beforeText = block.value.substring(lastIndex, match.index);
          
          // 添加占位符前的文本
          if (beforeText) {
            textParts.push(
              <span key={`text-${blockIndex}-${lastIndex}`}>
                {beforeText}
              </span>
            );
          }

          // 添加填空输入框
          textParts.push(
            renderBlankInput(
              blankId,
              currentAnswer[blankId] || '',
              (value) => handleBlankChange(blankId, value),
              {
                key: `blank-${blankId}`,
                status: getBlankStatus(blankId),
                correctAnswer: getCorrectAnswers()[blankId]
              }
            )
          );

          lastIndex = match.index + match[0].length;
        }

        // 添加最后剩余的文本
        const remainingText = block.value.substring(lastIndex);
        if (remainingText) {
          textParts.push(
            <span key={`text-${blockIndex}-${lastIndex}`}>
              {remainingText}
            </span>
          );
        }

        return (
          <div key={blockIndex} className="content-block-with-blanks">
            {textParts.length > 0 ? textParts : block.value}
          </div>
        );
      } else {
        // 非文本块直接渲染
        return (
          <ContentBlockList
            key={blockIndex}
            contentBlocks={[block]}
            onError={onError}
          />
        );
      }
    });
  };

  // 渲染独立的填空列表（如果没有内联填空）
  const renderStandaloneBlanks = () => {
    const correctAnswers = getCorrectAnswers();
    const blankIds = Object.keys(correctAnswers);

    if (blankIds.length === 0) return null;

    return (
      <div className="standalone-blanks">
        <div className="blanks-title">请填写答案：</div>
        {blankIds.map((blankId, index) => (
          <div key={blankId} className="blank-item">
            <span className="blank-label">({index + 1}) </span>
            {renderBlankInput(
              blankId,
              currentAnswer[blankId] || '',
              (value) => handleBlankChange(blankId, value),
              {
                status: getBlankStatus(blankId),
                correctAnswer: correctAnswers[blankId],
                style: { width: 200 }
              }
            )}
          </div>
        ))}
      </div>
    );
  };

  // 检查是否有内联填空
  const hasInlineBlanks = isHtmlFormat ? false : (
    Array.isArray(content) && content.some(block =>
      block.type === 'TEXT' && block.value && /___\w+___/.test(block.value)
    )
  );

  // 渲染填空题内容
  const renderQuestionContent = () => {
    return (
      <div className={`fill-in-blank-question ${className}`} style={style}>
        {/* 题目内容 */}
        <div className="question-content">
          {isHtmlFormat ? (
            hasUnderlines(content) ? (
              // HTML格式且包含下划线：使用新的统一渲染器
              <UnifiedBlankRenderer
                htmlContent={content}
                answers={currentAnswer}
                onAnswerChange={onAnswerChange}
                disabled={disabled || showResult}
                showResult={showResult}
                result={{
                  ...result,
                  answer: answer, // 传递原始答案数据
                  correctAnswers: getCorrectAnswers() // 传递处理后的正确答案
                }}
                subject={subject}
                onError={onError}
                onValidationResult={(validationResult) => {
                  // 移除调试日志，避免循环输出
                  // console.log('FillInBlankQuestion收到验证结果:', validationResult);
                  // 这里可以更新题目的整体验证状态
                  // 如果有需要，可以通过props传递给父组件
                }}
              />
            ) : (
              // HTML格式但无下划线：使用普通渲染器
              <HtmlContentRenderer
                htmlContent={content}
                onError={onError}
              />
            )
          ) : (
            // ContentBlock格式：检查是否有内联填空
            hasInlineBlanks ? renderContentWithBlanks() : (
              <ContentBlockList
                contentBlocks={content}
                onError={onError}
              />
            )
          )}
        </div>

        {/* 独立填空列表 - 只在没有内联填空时显示 */}
        {((isHtmlFormat && !hasUnderlines(content)) || (!isHtmlFormat && !hasInlineBlanks)) && renderStandaloneBlanks()}

        {/* 统一解释块 - 包含答案和解析 */}
        {showResult && result && (
          <div className="question-explanation-block">
            {/* 答案部分 */}
            <div className="explanation-answer">
              <span className="explanation-label">答案：</span>
              <div className="explanation-content">
                {(() => {
                  const correctAnswers = getCorrectAnswers();
                  const answerEntries = Object.entries(correctAnswers);

                  // 将所有答案用逗号分隔显示，不显示"第X个空"标识
                  return answerEntries.map(([blankId, answer], index) => (
                    <span key={blankId}>
                      {renderAnswerContent(answer)}
                      {index < answerEntries.length - 1 ? ', ' : ''}
                    </span>
                  ));
                })()}
              </div>
            </div>

            {/* 解析部分 */}
            {result.explanation && (
              <div className="explanation-analysis">
                <span className="explanation-label">解析：</span>
                <div className="explanation-content">
                  <ContentBlockList
                    contentBlocks={result.explanation}
                    onError={onError}
                  />
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  // 直接返回内容，使用全局 MathJax 上下文
  return renderQuestionContent();
};

FillInBlankQuestion.propTypes = {
  questionData: PropTypes.shape({
    // 支持多种数据格式
    content: PropTypes.oneOfType([PropTypes.array, PropTypes.string]),
    answer: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.array,
      PropTypes.shape({
        value: PropTypes.object.isRequired
      })
    ]),
    // 新的JSON格式支持
    questionDetails: PropTypes.shape({
      content: PropTypes.oneOfType([PropTypes.array, PropTypes.string]),
      answer: PropTypes.oneOfType([PropTypes.string, PropTypes.array]),
      explanation: PropTypes.string
    }),
    explanation: PropTypes.string
  }).isRequired,
  userAnswer: PropTypes.object,
  onAnswerChange: PropTypes.func,
  disabled: PropTypes.bool,
  showResult: PropTypes.bool,
  result: PropTypes.shape({
    isCorrect: PropTypes.bool.isRequired,
    explanation: PropTypes.oneOfType([PropTypes.string, PropTypes.array])
  }),
  className: PropTypes.string,
  style: PropTypes.object,
  onError: PropTypes.func
};

export default FillInBlankQuestion;
