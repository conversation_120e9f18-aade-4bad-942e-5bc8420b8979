/**
 * 学习模块状态管理Reducer
 */

// 学习阶段常量
export const LEARNING_STAGES = {
  INTRO: 'INTRO',
  VIDEO: 'VIDEO',
  QUESTIONS: 'QUESTIONS',
  RESULT: 'RESULT',
  REVIEW: 'REVIEW'  // 新增：查看题目阶段
};

// 初始状态
export const initialState = {
  knowledgePoint: null,
  questions: [],                    // 题目列表
  currentStage: 'INTRO',           // 当前学习阶段
  currentQuestionIndex: 0,         // 当前题目索引
  userAnswers: {},
  userSelection: {},               // 用户当前选择但尚未提交的答案
  answerVerified: false,           // 标记当前题目是否已验证答案
  showingAnswer: false,            // 标记是否正在显示答案和解析
  finalResult: null,
  testStartTime: null,             // 测试开始时间
  testEndTime: null,               // 测试结束时间
  loading: false,
  error: null
};

/**
 * 学习模块Reducer
 * 
 * @param {Object} state 当前状态
 * @param {Object} action 动作对象
 * @returns {Object} 新状态
 */
export const learningReducer = (state, action) => {
  switch (action.type) {
    case 'FETCH_START':
      return {
        ...state,
        loading: true,
        error: null
      };
      
    case 'FETCH_SUCCESS':
      // 确保数据结构正确，安全处理undefined payload
      const payload = action.payload || {};
      const knowledgePoint = payload.knowledgePoint || payload;
      const questions = payload.questions;

      console.log('Reducer处理的数据:', { knowledgePoint, questions, hasQuestions: !!questions });

      // 如果payload为空对象，只更新loading状态，不重置数据
      if (!knowledgePoint && !questions) {
        console.log('Reducer - 空payload，仅更新loading状态');
        return {
          ...state,
          loading: false,
          error: null
        };
      }

      return {
        ...state,
        loading: false,
        error: null,
        knowledgePoint: knowledgePoint || state.knowledgePoint,
        questions: questions ? (Array.isArray(questions) ? questions : []) : state.questions,
        currentStage: knowledgePoint ? 'INTRO' : state.currentStage
      };
      
    case 'FETCH_ERROR':
      return {
        ...state,
        loading: false,
        error: action.payload
      };

    case 'FETCH_END':
      // 仅结束加载状态，不重置数据
      return {
        ...state,
        loading: false,
        error: null
      };

    case 'RESET_ERROR':
      return {
        ...state,
        error: null
      };
      
    case 'NEXT_STAGE':
      const stages = Object.values(LEARNING_STAGES);
      const currentIndex = stages.indexOf(state.currentStage);

      // 其他阶段进入下一阶段
      let nextStage = stages[currentIndex + 1];

      // 如果从INTRO阶段进入下一阶段，检查是否有视频内容
      if (state.currentStage === 'INTRO' && nextStage === 'VIDEO') {
        // 检查知识点是否有视频内容（videoCollectionId为空或null表示没有视频）
        const hasVideo = state.knowledgePoint?.videoCollectionId &&
                         state.knowledgePoint.videoCollectionId !== null &&
                         state.knowledgePoint.videoCollectionId !== '';

        console.log('检查视频内容:', {
          knowledgePointId: state.knowledgePoint?.id,
          knowledgePointName: state.knowledgePoint?.name,
          videoCollectionId: state.knowledgePoint?.videoCollectionId,
          hasVideo: hasVideo,
          questionsCount: state.questions?.length || 0
        });

        if (!hasVideo) {
          console.log('知识点没有视频内容，跳过VIDEO阶段');
          // 跳过VIDEO阶段，直接进入QUESTIONS阶段
          nextStage = 'QUESTIONS';

          // 如果也没有题目，则直接跳到RESULT阶段
          if (!state.questions || state.questions.length === 0) {
            console.log('也没有题目，直接跳到RESULT阶段');
            nextStage = 'RESULT';
          }
        } else {
          console.log('知识点有视频内容，正常进入VIDEO阶段');
        }
      }

      // 如果下一阶段是QUESTIONS但没有题目，跳过到RESULT
      if (nextStage === 'QUESTIONS' && (!state.questions || state.questions.length === 0)) {
        nextStage = 'RESULT';
      }

      // 如果进入QUESTIONS阶段，记录开始时间
      const newState = {
        ...state,
        currentStage: nextStage || state.currentStage
      };

      if (nextStage === 'QUESTIONS' && !state.testStartTime) {
        newState.testStartTime = Date.now();
      }

      return newState;

    // 新增：下一题（仅在QUESTIONS阶段使用）
    case 'NEXT_QUESTION':
      console.log('NEXT_QUESTION action triggered:', {
        currentStage: state.currentStage,
        currentQuestionIndex: state.currentQuestionIndex,
        totalQuestions: state.questions.length,
        answerVerified: state.answerVerified
      });

      if (state.currentStage !== 'QUESTIONS') {
        console.log('Not in QUESTIONS stage, ignoring NEXT_QUESTION');
        return state;
      }

      // 检查是否还有题目
      if (state.currentQuestionIndex < state.questions.length - 1) {
        // 下一题
        console.log('Moving to next question:', state.currentQuestionIndex + 1);
        return {
          ...state,
          currentQuestionIndex: state.currentQuestionIndex + 1,
          answerVerified: false,
          showingAnswer: false,
          userSelection: {}
        };
      } else {
        // 所有题目完成，进入结果页面
        console.log('All questions completed, moving to RESULT stage');
        return {
          ...state,
          currentStage: 'RESULT',
          testEndTime: Date.now() // 记录测试结束时间
        };
      }

    case 'PREV_STAGE':
      const prevStages = Object.values(LEARNING_STAGES);
      const prevIndex = prevStages.indexOf(state.currentStage);

      if (state.currentStage === 'QUESTIONS' && state.currentQuestionIndex > 0) {
        // 在练习阶段，返回上一题
        return {
          ...state,
          currentQuestionIndex: state.currentQuestionIndex - 1,
          answerVerified: false,
          showingAnswer: false,
          userSelection: {}
        };
      } else {
        // 返回上一阶段
        const prevStage = prevStages[prevIndex - 1];
        return {
          ...state,
          currentStage: prevStage || state.currentStage,
          currentQuestionIndex: 0
        };
      }
      
    case 'SAVE_USER_ANSWER':
      return {
        ...state,
        userAnswers: {
          ...state.userAnswers,
          [action.payload.questionId]: action.payload.answer
        }
      };
      
    case 'SET_FINAL_RESULT':
      return {
        ...state,
        finalResult: action.payload
      };
      
    case 'RESET_LEARNING':
      // 重置学习状态，但保留知识点和题目数据
      // 根据知识点的内容决定初始阶段
      const hasQuestions = state.questions && state.questions.length > 0;
      const hasVideo = state.knowledgePoint?.videoCollectionId &&
                       state.knowledgePoint.videoCollectionId !== null &&
                       state.knowledgePoint.videoCollectionId !== '';

      console.log('重置学习状态:', {
        knowledgePointId: state.knowledgePoint?.id,
        knowledgePointName: state.knowledgePoint?.name,
        hasVideo: hasVideo,
        hasQuestions: hasQuestions,
        videoCollectionId: state.knowledgePoint?.videoCollectionId,
        questionsCount: state.questions?.length || 0
      });

      // 决定重置后的初始阶段
      let resetStage = 'INTRO';
      if (hasQuestions && !hasVideo) {
        // 有题目但没有视频，直接跳到题目阶段
        resetStage = 'QUESTIONS';
        console.log('重置到QUESTIONS阶段（有题目但没有视频）');
      } else if (!hasQuestions && !hasVideo) {
        // 既没有视频也没有题目，直接跳到结果阶段
        resetStage = 'RESULT';
        console.log('重置到RESULT阶段（既没有视频也没有题目）');
      } else {
        console.log('重置到INTRO阶段（正常流程）');
      }

      const resetState = {
        ...initialState,
        knowledgePoint: state.knowledgePoint,
        questions: state.questions,
        currentStage: resetStage
      };

      // 如果重置后直接进入QUESTIONS阶段，记录开始时间
      if (resetStage === 'QUESTIONS') {
        resetState.testStartTime = Date.now();
      }

      return resetState;

    // 新增：使用新题目重新开始练习
    case 'RESTART_WITH_NEW_QUESTIONS':
      const { knowledgePoint: newKnowledgePoint, questions: newQuestions } = action.payload;

      console.log('使用新题目重新开始练习:', {
        knowledgePointId: newKnowledgePoint?.id,
        knowledgePointName: newKnowledgePoint?.name,
        newQuestionsCount: newQuestions?.length || 0
      });

      // 根据知识点的内容决定初始阶段
      const hasNewQuestions = newQuestions && newQuestions.length > 0;
      const hasNewVideo = newKnowledgePoint?.videoCollectionId &&
                         newKnowledgePoint.videoCollectionId !== null &&
                         newKnowledgePoint.videoCollectionId !== '';

      // 决定重新开始后的初始阶段
      let restartStage = 'INTRO';
      if (hasNewQuestions && !hasNewVideo) {
        // 有题目但没有视频，直接跳到题目阶段
        restartStage = 'QUESTIONS';
        console.log('重新开始到QUESTIONS阶段（有题目但没有视频）');
      } else if (!hasNewQuestions && !hasNewVideo) {
        // 既没有视频也没有题目，直接跳到结果阶段
        restartStage = 'RESULT';
        console.log('重新开始到RESULT阶段（既没有视频也没有题目）');
      } else {
        console.log('重新开始到INTRO阶段（正常流程）');
      }

      const restartState = {
        ...initialState,
        knowledgePoint: newKnowledgePoint,
        questions: newQuestions,
        currentStage: restartStage,
        loading: false, // 确保加载状态结束
        error: null
      };

      // 如果重新开始后直接进入QUESTIONS阶段，记录开始时间
      if (restartStage === 'QUESTIONS') {
        restartState.testStartTime = Date.now();
      }

      return restartState;

    // 新增：设置用户当前选择 - 优化避免不必要的更新
    case 'SET_USER_SELECTION':
      // 获取当前问题ID的选择
      const questionId = action.payload.questionId;
      const currentSelection = state.userSelection[questionId] || {};
      const newSelection = action.payload.selection;

      // 如果是填空题（即答案是对象），则合并所有空格的答案
      const combinedSelection =
        typeof newSelection === 'object' && newSelection !== null && !Array.isArray(newSelection)
          ? { ...currentSelection, ...newSelection }
          : newSelection; // 非填空题直接使用新选择

      // 检查是否真的有变化，避免不必要的更新
      const hasChanged = typeof combinedSelection === 'object' && combinedSelection !== null
        ? JSON.stringify(currentSelection) !== JSON.stringify(combinedSelection)
        : currentSelection !== combinedSelection;

      if (!hasChanged) {
        return state; // 没有变化，不更新状态
      }

      return {
        ...state,
        userSelection: {
          ...state.userSelection,
          [questionId]: combinedSelection
        }
      };
      
    // 新增：验证答案
    case 'VERIFY_ANSWER':
      const questionId = action.payload.questionId;
      const verificationResult = action.payload.verificationResult;

      // 更新题目数据，添加subResults（如果存在）
      const updatedQuestions = state.questions.map(question => {
        if (question.id === questionId && verificationResult?.subResults) {
          return {
            ...question,
            subResults: verificationResult.subResults
          };
        }
        return question;
      });

      return {
        ...state,
        answerVerified: true,
        questions: updatedQuestions,
        // 保存用户答案
        userAnswers: {
          ...state.userAnswers,
          [questionId]: {
            // 安全处理answer为null的情况（跳过题目时）
            ...(action.payload.answer || {}),
            isCorrect: verificationResult?.isCorrect || false,
            isSkipped: verificationResult?.isSkipped || false,
            feedback: verificationResult
          }
        }
      };
      
    // 新增：显示答案和解析
    case 'SHOW_ANSWER':
      return {
        ...state,
        showingAnswer: true
      };

    // 新增：重置答案状态
    case 'RESET_ANSWER_STATE':
      return {
        ...state,
        answerVerified: false,
        showingAnswer: false,
        userSelection: {}
      };

    // 新增：进入查看题目模式
    case 'ENTER_REVIEW_MODE':
      return {
        ...state,
        currentStage: 'REVIEW',
        currentQuestionIndex: 0,
        answerVerified: true,  // 在查看模式下显示答案
        showingAnswer: true
      };

    // 新增：查看模式下的题目导航
    case 'PREV_QUESTION':
      return {
        ...state,
        currentQuestionIndex: Math.max(0, state.currentQuestionIndex - 1)
      };

    case 'NEXT_QUESTION':
      return {
        ...state,
        currentQuestionIndex: Math.min(state.questions.length - 1, state.currentQuestionIndex + 1)
      };

    case 'GO_TO_RESULT':
      return {
        ...state,
        currentStage: 'RESULT'
      };

    default:
      return state;
  }
}; 