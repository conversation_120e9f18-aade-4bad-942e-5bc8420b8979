import { useState, useEffect, useRef, useCallback } from 'react';
import { Typography, Button, Modal, Tag, message } from 'antd';
import { CheckCircleOutlined, RightOutlined, CloseCircleOutlined, ExclamationCircleOutlined, PlayCircleOutlined, CloseOutlined } from '@ant-design/icons';
import ReactPlayer from 'react-player';
import PropTypes from 'prop-types';
import '../../../styles/SceneStyles.css';
import './ResultScreen.css';
import HtmlContentRenderer from '../../content/HtmlContentRenderer';
import { ContentBlockList } from '../../content/ContentBlockRenderer';
import { LearningAPI } from '../../../api/learning';
import { MathJax } from 'better-react-mathjax';
import QuestionDetailModal from '../../common/QuestionDetailModal';

const { Title } = Typography;

/**
 * 结果场景组件
 *
 * @param {Object} props 组件属性
 * @param {Object} props.knowledgePoint 知识点数据
 * @param {number} props.progress 学习进度
 * @param {Object} props.userAnswers 用户答案
 * @param {Array} props.questions 题目列表
 * @param {Function} props.onRestart 重新开始学习的回调函数
 * @param {Function} props.onViewQuestions 查看题目的回调函数
 * @param {number} props.sessionId 可选的练习会话ID，用于从数据库获取数据
 * @param {number} props.testStartTime 测试开始时间戳
 * @param {number} props.testEndTime 测试结束时间戳
 * @returns {JSX.Element} 结果场景组件
 */
const ResultScreen = ({ knowledgePoint, progress, userAnswers = {}, questions = [], onRestart, onViewQuestions, sessionId, isHistoryView = false, testStartTime, testEndTime }) => {
  console.log('ResultScreen - 组件初始化, props:', {
    knowledgePoint: knowledgePoint?.name,
    questionsCount: questions.length,
    userAnswersCount: Object.keys(userAnswers).length,
    sessionId: sessionId
  });

  // 状态管理
  const [selectedQuestion, setSelectedQuestion] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [practiceResultSaved, setPracticeResultSaved] = useState(false);
  const [dbData, setDbData] = useState(null);
  const [loadingDbData, setLoadingDbData] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState(sessionId);

  // 知识点解析弹窗状态
  const [knowledgeAnalysisVisible, setKnowledgeAnalysisVisible] = useState(false);
  const [currentVideo, setCurrentVideo] = useState(null);
  const [playing, setPlaying] = useState(false);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [videoList, setVideoList] = useState([]);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [loadingVideos, setLoadingVideos] = useState(false);

  console.log('ResultScreen - 初始状态:', {
    currentSessionId,
    hasDbData: !!dbData,
    loadingDbData
  });

  // 使用ref防止重复保存
  const saveInProgress = useRef(false);

  // 从数据库获取练习数据
  useEffect(() => {
    console.log('ResultScreen - useEffect触发, currentSessionId:', currentSessionId, 'dbData:', !!dbData, 'loadingDbData:', loadingDbData);

    const fetchPracticeData = async () => {
      // 如果提供了sessionId，从数据库获取数据
      if (currentSessionId && !dbData && !loadingDbData) {
        console.log('ResultScreen - 开始从数据库获取练习数据, sessionId:', currentSessionId);
        setLoadingDbData(true);

        try {
          console.log('ResultScreen - 调用getPracticeDetail API');
          const practiceDetail = await LearningAPI.getPracticeDetail(currentSessionId);
          console.log('ResultScreen - getPracticeDetail API响应:', practiceDetail);

          const convertedData = await LearningAPI.convertPracticeDetailToProps(practiceDetail);
          console.log('ResultScreen - 数据转换结果:', convertedData);

          if (convertedData) {
            console.log('ResultScreen - 数据库数据获取成功，设置dbData');
            setDbData(convertedData);
          } else {
            console.warn('ResultScreen - 数据库数据转换失败');
          }
        } catch (error) {
          console.error('ResultScreen - 获取数据库数据失败:', error);
        } finally {
          console.log('ResultScreen - 数据获取完成，设置loadingDbData为false');
          setLoadingDbData(false);
        }
      } else {
        console.log('ResultScreen - 跳过数据获取，原因:', {
          hasSessionId: !!currentSessionId,
          hasDbData: !!dbData,
          isLoading: loadingDbData
        });
      }
    };

    fetchPracticeData();
  }, [currentSessionId, dbData, loadingDbData]);

  // 确定数据来源：优先使用数据库数据，然后是props数据
  const effectiveKnowledgePoint = dbData?.knowledgePoint || knowledgePoint;
  const effectiveQuestions = dbData?.questions || questions;
  const effectiveUserAnswers = dbData?.userAnswers || userAnswers;

  // 确保questions是数组，userAnswers是对象
  const validQuestions = Array.isArray(effectiveQuestions) ? effectiveQuestions : [];
  const validUserAnswers = effectiveUserAnswers && typeof effectiveUserAnswers === 'object' ? effectiveUserAnswers : {};

  // 保存练习结果
  useEffect(() => {
    const savePracticeResult = async () => {
      // 避免重复保存
      if (practiceResultSaved || saveInProgress.current || !knowledgePoint?.id || validQuestions.length === 0) {
        console.log('ResultScreen - 跳过保存:', {
          practiceResultSaved,
          saveInProgress: saveInProgress.current,
          hasKnowledgePoint: !!knowledgePoint?.id,
          questionsCount: validQuestions.length
        });
        return;
      }

      console.log('ResultScreen - 开始保存练习结果');
      saveInProgress.current = true;

      try {
        // 计算实际测试时长
        const actualDurationSeconds = testStartTime && testEndTime ?
          Math.floor((testEndTime - testStartTime) / 1000) :
          validQuestions.length * 60; // 如果没有时间记录，按每题1分钟估算

        // 构建练习数据
        const practiceData = {
          knowledgePointId: knowledgePoint.id,
          sessionType: 'KNOWLEDGE_POINT_PRACTICE',
          durationSeconds: actualDurationSeconds, // 添加实际测试时长
          questions: validQuestions.map((question, index) => {
            const questionAnswer = validUserAnswers[question.id];

            // 确定答题状态
            let answerStatus = 'NOT_ANSWERED';
            if (questionAnswer) {
              if (questionAnswer.isSkipped) {
                answerStatus = 'SKIPPED';
              } else if (questionAnswer.isCorrect) {
                answerStatus = 'CORRECT';
              } else {
                answerStatus = 'INCORRECT';
              }
            }

            // 获取学生的真实输入答案
            let studentAnswer = null;
            if (questionAnswer && !questionAnswer.isSkipped) {
              // 提取学生的原始输入数据（排除系统添加的字段）
              const { isCorrect, isSkipped, feedback, ...originalAnswer } = questionAnswer;
              if (Object.keys(originalAnswer).length > 0) {
                studentAnswer = JSON.stringify(originalAnswer);
              }
            }

            return {
              questionId: question.id,
              questionOrder: index + 1,
              questionSnapshot: JSON.stringify(question),
              studentAnswer: studentAnswer,
              correctAnswer: JSON.stringify(question.questionDetails?.answer || question.answer || ''),
              answerStatus: answerStatus
            };
          })
        };

        console.log('保存练习结果数据:', practiceData);

        const response = await LearningAPI.savePracticeResult(practiceData);
        console.log('练习结果保存成功:', response);

        setPracticeResultSaved(true);
        message.success('练习结果已保存', 2);

        // 如果返回了sessionId，保存它以便后续从数据库获取数据
        if (response?.sessionId && !currentSessionId) {
          console.log('ResultScreen - 保存sessionId用于数据库查询:', response.sessionId);
          setCurrentSessionId(response.sessionId);
        }

      } catch (error) {
        console.error('保存练习结果失败:', error);
        // 不显示错误消息，避免影响用户体验
      } finally {
        saveInProgress.current = false;
      }
    };

    savePracticeResult();
  }, [knowledgePoint?.id, validQuestions, validUserAnswers, practiceResultSaved]);
  
  // 处理题目点击
  const handleQuestionClick = (questionIndex) => {
    const question = validQuestions[questionIndex];
    const questionAnswer = validUserAnswers[question?.id];

    if (question) {
      setSelectedQuestion({
        question,
        questionAnswer,
        index: questionIndex + 1
      });
      setModalVisible(true);
    }
  };

  // 关闭模态框
  const handleModalClose = () => {
    setModalVisible(false);
    setSelectedQuestion(null);
  };

  // 处理知识点解析按钮点击
  const handleKnowledgeAnalysis = async () => {
    console.log('打开知识点解析弹窗, 知识点:', knowledgePoint);

    if (!knowledgePoint?.videoCollectionId) {
      message.warning('该知识点暂无解析视频');
      return;
    }

    setLoadingVideos(true);

    try {
      // 从API获取知识点对应的视频合集中的视频
      const response = await LearningAPI.getVideosByCollection(knowledgePoint.videoCollectionId);
      console.log('获取视频数据成功:', response);

      // 检查API返回的数据结构
      const videoData = response.data || response;
      if (videoData && videoData.length > 0) {
        // 处理视频数据
        const videos = videoData.map(video => ({
          id: video.id,
          title: video.title || `${knowledgePoint.name}解析视频`,
          videoUrl: video.videoUrl,
          coverImageUrl: video.coverImageUrl,
          description: video.description,
          durationSeconds: video.durationSeconds
        }));

        setVideoList(videos);
        setCurrentVideo(videos[0]); // 设置第一个视频为当前视频
        setCurrentVideoIndex(0);
        setKnowledgeAnalysisVisible(true);
      } else {
        message.warning('该知识点暂无可用的解析视频');
      }
    } catch (error) {
      console.error('获取知识点视频失败:', error);
      message.error('获取解析视频失败，请稍后重试');
    } finally {
      setLoadingVideos(false);
    }
  };

  // 关闭知识点解析弹窗
  const handleKnowledgeAnalysisClose = () => {
    setKnowledgeAnalysisVisible(false);
    setPlaying(false);
    setCurrentVideo(null);
    setVideoList([]);
    setCurrentVideoIndex(0);
    setLoadingVideos(false);
  };

  // 切换视频
  const handleVideoSelect = (index) => {
    if (index >= 0 && index < videoList.length) {
      setCurrentVideoIndex(index);
      setCurrentVideo(videoList[index]);
      setPlaying(false); // 切换视频时暂停播放
    }
  };

  // 渲染答案内容，支持HTML和数学公式
  const renderAnswerContent = (content) => {
    if (!content) return '';

    const contentStr = String(content);

    // 检查是否包含HTML标签
    if (contentStr.includes('<')) {
      return (
        <HtmlContentRenderer
          htmlContent={contentStr}
          onError={(error) => console.error('HTML渲染错误:', error)}
        />
      );
    }

    // 检查是否包含LaTeX数学公式
    if (contentStr.includes('\\') || contentStr.includes('{') || contentStr.includes('^') || contentStr.includes('_')) {
      return (
        <div style={{ textAlign: 'left' }}>
          <MathJax>
            {`$${contentStr}$`}
          </MathJax>
        </div>
      );
    }

    // 普通文本
    return contentStr;
  };

  // 渲染学生答案
  const renderStudentAnswer = (questionAnswer, question) => {
    try {
      // 优先使用submittedAnswer字段，如果没有则直接使用questionAnswer对象
      let submittedAnswer = {};
      if (questionAnswer.submittedAnswer) {
        submittedAnswer = JSON.parse(questionAnswer.submittedAnswer);
      } else {
        // 从questionAnswer对象中提取答案数据（排除系统字段）
        const { isCorrect, isSkipped, feedback, ...answerData } = questionAnswer;
        submittedAnswer = answerData;
      }

      const questionType = question.questionDetails?.type || question.questionType;

      switch (questionType) {
        case 'SINGLE_CHOICE':
        case 'MULTIPLE_CHOICE':
          // 获取用户选择的答案
          let userAnswer = '';
          if (submittedAnswer.selectedOptions) {
            userAnswer = submittedAnswer.selectedOptions[0]; // 单选题取第一个
          } else if (Object.keys(submittedAnswer).length > 0) {
            const values = Object.values(submittedAnswer).filter(v => v !== null && v !== undefined);
            userAnswer = values.length > 0 ? values[0] : '';
          }

          // 获取题目选项
          const options = question.questionDetails?.options || question.options;
          const correctAnswer = question.questionDetails?.answer || question.answer;

          if (options && Array.isArray(options)) {
            return (
              <div>
                {options.map((option, optionIndex) => {
                  const optionLetter = String.fromCharCode(65 + optionIndex); // A, B, C, D
                  const isSelected = userAnswer === optionLetter;
                  const isCorrect = correctAnswer === optionLetter;

                  // 确定样式
                  let backgroundColor = 'transparent';
                  let borderColor = '#d9d9d9';
                  let textColor = '#000';
                  let icon = '';
                  let label = '';

                  if (isSelected && isCorrect) {
                    // 用户选择且正确
                    backgroundColor = '#f6ffed';
                    borderColor = '#52c41a';
                    textColor = '#52c41a';
                    icon = '✓';
                    label = '(您的选择 - 正确)';
                  } else if (isSelected && !isCorrect) {
                    // 用户选择但错误
                    backgroundColor = '#fff2f0';
                    borderColor = '#ff4d4f';
                    textColor = '#ff4d4f';
                    icon = '✗';
                    label = '(您的选择 - 错误)';
                  } else if (!isSelected && isCorrect) {
                    // 正确答案但用户未选择
                    backgroundColor = '#f6ffed';
                    borderColor = '#52c41a';
                    textColor = '#52c41a';
                    icon = '✓';
                    label = '(正确答案)';
                  }

                  return (
                    <div
                      key={optionIndex}
                      style={{
                        marginBottom: '4px',
                        padding: '8px 12px',
                        borderRadius: '4px',
                        backgroundColor,
                        border: `2px solid ${borderColor}`,
                        fontWeight: (isSelected || isCorrect) ? 'bold' : 'normal',
                        color: textColor
                      }}
                    >
                      {icon && <span style={{ marginRight: '8px' }}>{icon}</span>}
                      {optionLetter}. <HtmlContentRenderer htmlContent={option} />
                      {label && <span style={{ marginLeft: '8px' }}>{label}</span>}
                    </div>
                  );
                })}
              </div>
            );
          }

          // 降级处理：如果没有选项数据，显示字母
          return userAnswer || '未选择';

        case 'TRUE_FALSE':
          return submittedAnswer.answer !== undefined ?
            (submittedAnswer.answer ? '正确' : '错误') : '未选择';

        case 'FILL_IN_BLANK':
          if (submittedAnswer && typeof submittedAnswer === 'object') {
            const answers = [];
            Object.keys(submittedAnswer).sort().forEach(key => {
              if (key.startsWith('blank_')) {
                const answerValue = submittedAnswer[key] || '(空)';
                // 使用renderAnswerContent渲染每个填空答案，支持数学公式
                answers.push(renderAnswerContent(answerValue));
              }
            });
            if (answers.length > 0) {
              // 如果只有一个答案，直接返回
              if (answers.length === 1) {
                return answers[0];
              }
              // 多个答案时，用逗号分隔显示
              return (
                <div>
                  {answers.map((answer, index) => (
                    <span key={index}>
                      {answer}
                      {index < answers.length - 1 && ', '}
                    </span>
                  ))}
                </div>
              );
            }
            return '未填写';
          }
          return '未填写';

        default:
          return JSON.stringify(submittedAnswer);
      }
    } catch (error) {
      return '答案解析失败';
    }
  };

  // 渲染正确答案
  const renderCorrectAnswer = (question) => {
    try {
      const questionDetails = question.questionDetails;
      const questionType = questionDetails?.type || question.questionType;
      const answer = questionDetails?.answer;

      // 处理嵌套题型（阅读理解、听力题等）
      if (questionType === 'READING_COMPREHENSION' || questionType === 'LISTENING' || questionType === 'CLOZE_TEST') {
        console.log('renderCorrectAnswer - 处理嵌套题型:', questionType);
        console.log('questionDetails:', questionDetails);
        console.log('question:', question);

        // 首先尝试从subResults中获取正确答案（这是答题后的数据）
        if (question.subResults && typeof question.subResults === 'object') {
          console.log('从subResults获取正确答案:', question.subResults);
          const subAnswers = [];
          Object.entries(question.subResults).forEach(([subQuestionId, subResult], index) => {
            if (subResult && subResult.correctAnswer) {
              subAnswers.push(`第${index + 1}题：${subResult.correctAnswer}`);
            } else {
              subAnswers.push(`第${index + 1}题：答案未设置`);
            }
          });
          if (subAnswers.length > 0) {
            return subAnswers.join(', ');
          }
        }

        // 检查是否有子题目
        if (questionDetails?.subQuestions && Array.isArray(questionDetails.subQuestions)) {
          const subAnswers = [];
          questionDetails.subQuestions.forEach((subQuestion, index) => {
            const subAnswer = subQuestion.answer;
            if (subAnswer) {
              subAnswers.push(`第${index + 1}题：${subAnswer}`);
            } else {
              subAnswers.push(`第${index + 1}题：答案未设置`);
            }
          });
          return subAnswers.join(', ');
        }

        // 如果没有子题目但有answer字段，尝试解析
        if (answer) {
          if (typeof answer === 'string') {
            try {
              const parsedAnswer = JSON.parse(answer);
              if (typeof parsedAnswer === 'object' && parsedAnswer !== null) {
                const answerEntries = Object.entries(parsedAnswer);
                return answerEntries.map(([key, value]) => `${key}：${value}`).join(', ');
              }
            } catch (e) {
              // 解析失败，直接返回字符串
              return answer;
            }
          } else if (typeof answer === 'object' && answer !== null) {
            const answerEntries = Object.entries(answer);
            return answerEntries.map(([key, value]) => `${key}：${value}`).join(', ');
          }
        }

        return '答案未设置';
      }

      switch (questionType) {
        case 'SINGLE_CHOICE':
        case 'MULTIPLE_CHOICE':
          if (Array.isArray(answer)) {
            return answer.join(', ');
          }
          return answer || '答案未设置';

        case 'TRUE_FALSE':
          return answer === true ? '正确' : answer === false ? '错误' : '答案未设置';

        case 'FILL_IN_BLANK':
          if (Array.isArray(answer)) {
            return answer.join(', ');
          }
          return answer || '答案未设置';

        default:
          return answer ? String(answer) : '答案未设置';
      }
    } catch (error) {
      console.error('renderCorrectAnswer 错误:', error);
      return '答案解析失败';
    }
  };

  // 渲染正确答案（支持HTML）
  const renderCorrectAnswerWithHtml = (question) => {
    try {
      const questionDetails = question.questionDetails;
      const questionType = questionDetails?.type || question.questionType;
      const answer = questionDetails?.answer;

      // 处理嵌套题型（阅读理解、听力题等）
      if (questionType === 'READING_COMPREHENSION' || questionType === 'LISTENING' || questionType === 'CLOZE_TEST') {
        console.log('renderCorrectAnswerWithHtml - 处理嵌套题型:', questionType);
        console.log('questionDetails:', questionDetails);
        console.log('question:', question);

        // 首先尝试从subResults中获取正确答案（这是答题后的数据）
        if (question.subResults && typeof question.subResults === 'object') {
          console.log('从subResults获取正确答案:', question.subResults);
          return (
            <div>
              {Object.entries(question.subResults).map(([subQuestionId, subResult], index) => (
                <div key={subQuestionId} style={{ marginBottom: '8px' }}>
                  <strong>第{index + 1}题：</strong>
                  {subResult && subResult.correctAnswer ? (
                    typeof subResult.correctAnswer === 'string' && subResult.correctAnswer.includes('<') ? (
                      <HtmlContentRenderer htmlContent={subResult.correctAnswer} />
                    ) : (
                      String(subResult.correctAnswer)
                    )
                  ) : (
                    '答案未设置'
                  )}
                </div>
              ))}
            </div>
          );
        }

        // 检查是否有子题目
        if (questionDetails?.subQuestions && Array.isArray(questionDetails.subQuestions)) {
          return (
            <div>
              {questionDetails.subQuestions.map((subQuestion, index) => (
                <div key={index} style={{ marginBottom: '8px' }}>
                  <strong>第{index + 1}题：</strong>
                  {subQuestion.answer ? (
                    subQuestion.answer.includes('<') ? (
                      <HtmlContentRenderer htmlContent={subQuestion.answer} />
                    ) : (
                      subQuestion.answer
                    )
                  ) : (
                    '答案未设置'
                  )}
                </div>
              ))}
            </div>
          );
        }

        // 如果没有子题目但有answer字段，尝试解析
        if (answer) {
          if (typeof answer === 'string') {
            try {
              const parsedAnswer = JSON.parse(answer);
              if (typeof parsedAnswer === 'object' && parsedAnswer !== null) {
                return (
                  <div>
                    {Object.entries(parsedAnswer).map(([key, value], index) => (
                      <div key={index} style={{ marginBottom: '4px' }}>
                        <strong>{key}：</strong>
                        {typeof value === 'string' && value.includes('<') ? (
                          <HtmlContentRenderer htmlContent={value} />
                        ) : (
                          String(value)
                        )}
                      </div>
                    ))}
                  </div>
                );
              }
            } catch (e) {
              // 解析失败，直接返回字符串
              if (answer.includes('<')) {
                return <HtmlContentRenderer htmlContent={answer} />;
              }
              return answer;
            }
          } else if (typeof answer === 'object' && answer !== null) {
            return (
              <div>
                {Object.entries(answer).map(([key, value], index) => (
                  <div key={index} style={{ marginBottom: '4px' }}>
                    <strong>{key}：</strong>
                    {typeof value === 'string' && value.includes('<') ? (
                      <HtmlContentRenderer htmlContent={value} />
                    ) : (
                      String(value)
                    )}
                  </div>
                ))}
              </div>
            );
          }
        }

        return '答案未设置';
      }

      switch (questionType) {
        case 'SINGLE_CHOICE':
        case 'MULTIPLE_CHOICE':
          // 获取题目选项
          const options = questionDetails?.options || question.options;

          if (options && Array.isArray(options) && answer) {
            // 找到正确答案对应的选项内容
            const answerIndex = answer.charCodeAt(0) - 65; // A=0, B=1, C=2, D=3
            if (answerIndex >= 0 && answerIndex < options.length) {
              const correctOptionContent = options[answerIndex];
              return (
                <div>
                  <strong>{answer}. </strong>
                  <HtmlContentRenderer htmlContent={correctOptionContent} />
                </div>
              );
            }
          }

          // 降级处理
          if (Array.isArray(answer)) {
            const answerText = answer.join(', ');
            if (answerText.includes('<')) {
              return <HtmlContentRenderer htmlContent={answerText} />;
            }
            return answerText;
          }
          if (answer && typeof answer === 'string' && answer.includes('<')) {
            return <HtmlContentRenderer htmlContent={answer} />;
          }
          return answer || '答案未设置';

        case 'TRUE_FALSE':
          return answer === true ? '正确' : answer === false ? '错误' : '答案未设置';

        case 'FILL_IN_BLANK':
          if (Array.isArray(answer)) {
            const answerText = answer.join(', ');
            // 如果答案包含HTML标签，使用HtmlContentRenderer
            if (answerText.includes('<')) {
              return <HtmlContentRenderer htmlContent={answerText} />;
            }
            return answerText;
          }
          // 如果答案包含HTML标签，使用HtmlContentRenderer
          if (answer && typeof answer === 'string' && answer.includes('<')) {
            return <HtmlContentRenderer htmlContent={answer} />;
          }
          return answer || '答案未设置';

        default:
          if (answer && typeof answer === 'string' && answer.includes('<')) {
            return <HtmlContentRenderer htmlContent={answer} />;
          }
          return answer ? String(answer) : '答案未设置';
      }
    } catch (error) {
      console.error('renderCorrectAnswerWithHtml 错误:', error);
      return '答案解析失败';
    }
  };

  // 渲染填空题，将学生答案显示在下划线中
  const renderFillInBlankWithAnswers = useCallback((questionContent, answerData, correctAnswers, questionResult = null) => {
    try {
      let content = questionContent;
      let blankIndex = 0;

      // 将下划线替换为学生答案
      content = content.replace(/_+/g, () => {
        const blankId = `blank_${blankIndex}`;
        const userAnswer = answerData[blankId] || '';

        // 处理正确答案 - 可能是数组格式
        let correctAnswer = '';
        if (Array.isArray(correctAnswers)) {
          correctAnswer = correctAnswers[blankIndex] || '';
        } else if (correctAnswers[blankId]) {
          correctAnswer = correctAnswers[blankId];
        }

        // 🔧 使用后端返回的整体验证结果，而不是前端重复验证
        // 如果有后端验证结果，使用后端结果；否则降级为简单比较
        let isCorrect = false;
        if (questionResult && questionResult.isCorrect !== undefined) {
          // 使用后端的整体验证结果
          isCorrect = questionResult.isCorrect;
        } else {
          // 降级处理：简单比较答案是否正确
          isCorrect = userAnswer.trim().toLowerCase() === correctAnswer.trim().toLowerCase();
        }
        const statusIcon = isCorrect ? '✓' : '✗';
        const statusColor = isCorrect ? '#52c41a' : '#ff4d4f';

        blankIndex++;

        // 如果有答案，显示答案和状态图标
        if (userAnswer) {
          return `<span style="display: inline-block; min-width: 60px; padding: 2px 8px; border-bottom: 2px solid ${statusColor}; background: transparent; text-align: center; position: relative;">
            ${userAnswer} <span style="color: ${statusColor}; font-weight: bold; margin-left: 4px;">${statusIcon}</span>
          </span>`;
        } else {
          // 没有答案，显示空白和错误图标
          return `<span style="display: inline-block; min-width: 60px; padding: 2px 8px; border-bottom: 2px solid #ff4d4f; background: transparent; text-align: center; position: relative;">
            <span style="color: #999;">(空)</span> <span style="color: #ff4d4f; font-weight: bold; margin-left: 4px;">✗</span>
          </span>`;
        }
      });

      return <HtmlContentRenderer htmlContent={content} />;
    } catch (error) {
      console.error('渲染填空题答案失败:', error);
      return '渲染失败';
    }
  }, []);

  // 如果正在加载数据库数据，显示加载状态
  if (loadingDbData) {
    return (
      <div className="scene-container result-screen">
        <div className="loading-spinner-container">
          <div style={{ fontSize: '18px', marginBottom: '20px', textAlign: 'center' }}>正在加载练习结果...</div>
          <div className="spinner-lg"></div>
        </div>
      </div>
    );
  }

  // 计算统计数据
  const hasQuestions = validQuestions.length > 0;

  // 简单的分数计算逻辑 - 基于已答题目的正确率
  const calculateSimpleScore = (questions, userAnswers) => {
    if (!questions.length) {
      return { correctCount: 0, totalCount: 0, answeredCount: 0, accuracy: 0 };
    }

    let correctCount = 0;
    let answeredCount = 0;

    questions.forEach(question => {
      const userAnswer = userAnswers[question.id];
      if (userAnswer) {
        answeredCount++;
        if (userAnswer.isCorrect) {
          correctCount++;
        }
      }
    });

    return {
      correctCount,
      totalCount: questions.length,
      answeredCount,
      // 基于已答题目的正确率：答对的题目数 / 已答题目数
      accuracy: answeredCount > 0 ? correctCount / answeredCount : 0
    };
  };

  const scoreResult = calculateSimpleScore(validQuestions, validUserAnswers);

  // 兼容旧的stats格式
  const stats = {
    completionRate: scoreResult.totalCount > 0 ? (scoreResult.answeredCount / scoreResult.totalCount) * 100 : 0,
    correctRate: scoreResult.accuracy || 0,
    answeredQuestions: scoreResult.answeredCount || 0,
    totalQuestions: scoreResult.totalCount || 0
  };

  // 计算掌握状态
  const masteryLevel = stats.correctRate >= 0.8 ? '已掌握' : stats.correctRate >= 0.6 ? '基本掌握' : '未掌握';
  const masteryIcon = stats.correctRate >= 0.8 ? '😊' : stats.correctRate >= 0.6 ? '😐' : '😞';
  const masteryColor = stats.correctRate >= 0.8 ? '#52c41a' : stats.correctRate >= 0.6 ? '#faad14' : '#ff4d4f';

  return (
    <div className="scene-container result-screen">
      {/* 主要内容区域 - 使用bg1.png背景 */}
      <div
        className="result-main-content"
        style={{
          backgroundImage: `url(${process.env.PUBLIC_URL}/images/bg1.png)`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        {/* 结果卡片 - 优化布局 */}
        <div className="result-card-optimized">
          {/* 头像和标题区域 */}
          <div className="result-header-section">
            <div className="result-avatar">
              <span style={{ fontSize: '48px' }}>{masteryIcon}</span>
            </div>
            <Title level={3} className="result-title">
              {masteryLevel}
            </Title>
          </div>

          {/* 知识点名称 */}
          <div className="result-knowledge-section">
            <Title level={2} className="knowledge-point-name">
              {effectiveKnowledgePoint?.name || '知识点'}
            </Title>
          </div>

          <div className="result-progress-bar">
            <div className="progress-container">
              <div
                className="progress-fill"
                style={{
                  width: `${Math.round(stats.correctRate * 100)}%`,
                  backgroundColor: masteryColor
                }}
              ></div>
            </div>
            <span className="progress-text">{Math.round(stats.correctRate * 100)}%</span>
          </div>
        </div>

        {/* 历史查看提示 */}
        {isHistoryView && (
          <div style={{ textAlign: 'center', margin: '20px 0', color: '#666', fontSize: '14px' }}>
            📚 这是您的历史练习记录
          </div>
        )}

        {/* 知识点解析按钮 */}
        <div style={{ textAlign: 'center', margin: '20px 0' }}>
          <Button
            className="knowledge-analysis-button"
            style={{
              background: 'linear-gradient(135deg, #FF8A4C 0%, #F17A31 100%)',
              border: '2px solid #D16428',
              color: '#FFFFFF',
              fontWeight: '600',
              borderRadius: '24px',
              padding: '8px 24px',
              height: '40px',
              fontSize: '14px',
              boxShadow: '0 4px 12px rgba(241, 122, 49, 0.3)',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.target.style.background = 'linear-gradient(135deg, #FF9A5C 0%, #E16A28 100%)';
              e.target.style.transform = 'translateY(-2px)';
              e.target.style.boxShadow = '0 6px 16px rgba(241, 122, 49, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.target.style.background = 'linear-gradient(135deg, #FF8A4C 0%, #F17A31 100%)';
              e.target.style.transform = 'translateY(0)';
              e.target.style.boxShadow = '0 4px 12px rgba(241, 122, 49, 0.3)';
            }}
            onClick={handleKnowledgeAnalysis}
          >
            知识点解析
          </Button>
        </div>

        {/* 操作区域 - 紧贴结果卡片 */}
        <div className="result-actions-area">
          {/* 错题统计 */}
          {hasQuestions && (
            <div className="wrong-questions-summary">
              <span className="summary-label">查看题目：</span>
              {validQuestions.map((_, index) => {
                const questionAnswer = validUserAnswers[validQuestions[index]?.id];
                const isCorrect = questionAnswer?.isCorrect;
                const isSkipped = questionAnswer?.isSkipped;
                return (
                  <span
                    key={index}
                    className={`result-question-number ${
                      isSkipped ? 'skipped' : (isCorrect ? 'correct' : 'wrong')
                    }`}
                    onClick={() => handleQuestionClick(index)}
                    style={{ cursor: 'pointer' }}
                    title="点击查看题目详情"
                  >
                    {index + 1}
                  </span>
                );
              })}
            </div>
          )}

          {/* 统一的按钮组 */}
          <div className="result-button-group">
            {onRestart && (
              <Button
                className="restart-button"
                onClick={onRestart}
                style={{
                  background: '#FFFFFF',
                  border: '2px solid #FF8A4C',
                  color: '#FF8A4C',
                  fontWeight: '600',
                  borderRadius: '24px',
                  padding: '8px 24px',
                  height: '40px',
                  fontSize: '14px',
                  width: '120px',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.target.style.background = '#FFF5F0';
                  e.target.style.transform = 'translateY(-2px)';
                  e.target.style.boxShadow = '0 4px 12px rgba(255, 138, 76, 0.3)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.background = '#FFFFFF';
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = 'none';
                }}
              >
                再来一次
              </Button>
            )}
            <Button
              type="primary"
              size="large"
              className="primary-button"
              onClick={() => window.history.back()}
            >
              返回
            </Button>
          </div>
        </div>
      </div>

      {/* 统一的题目详情弹窗 */}
      <QuestionDetailModal
        visible={modalVisible}
        onClose={handleModalClose}
        selectedQuestion={selectedQuestion}
        renderStudentAnswer={renderStudentAnswer}
        renderCorrectAnswer={renderCorrectAnswerWithHtml}
        renderFillInBlankWithAnswers={renderFillInBlankWithAnswers}
      />

      {/* 知识点解析弹窗 */}
      <Modal
        title={null}
        open={knowledgeAnalysisVisible}
        onCancel={handleKnowledgeAnalysisClose}
        footer={null}
        width={1200}
        centered
        closable={false}
        className="knowledge-analysis-modal"
        styles={{
          body: { padding: 0 }
        }}
      >
        {/* 自定义标签块 */}
        <div className="task-kpoint__exp__tabs" style={{
          background: 'rgba(0, 0, 0, 0.8)',
          padding: '0',
          position: 'relative',
          width: '100%'
        }}>
          <div style={{
            width: '100%',
            padding: '12px 20px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-start',
            position: 'relative'
          }}>
            <div className="task-kpoint__exp__tab task-kpoint__exp__tab_active" style={{
              background: '#ff6b35',
              color: '#ffffff',
              padding: '8px 16px',
              borderRadius: '6px 6px 0 0',
              fontSize: '14px',
              fontWeight: '600',
              marginRight: 'auto'
            }}>
              知识点视频
            </div>
            <div className="task-kpoint__exp__close" onClick={handleKnowledgeAnalysisClose} style={{
              cursor: 'pointer',
              color: '#ffffff',
              fontSize: '16px',
              padding: '4px',
              position: 'absolute',
              right: '20px',
              top: '50%',
              transform: 'translateY(-50%)'
            }}>
              <CloseOutlined />
            </div>
          </div>
        </div>

        <div className="video-analysis-container" style={{
          display: 'flex',
          height: '600px',
          background: '#ffffff'
        }}>
          {/* 左侧视频播放区域 - 80%宽度 */}
          <div style={{
            width: '80%',
            display: 'flex',
            flexDirection: 'column',
            background: '#ffffff',
            position: 'relative'
          }}>
            <div style={{
              flex: 1,
              position: 'relative',
              minHeight: '500px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              {loadingVideos ? (
                <div style={{
                  color: '#ffffff',
                  fontSize: '16px',
                  textAlign: 'center'
                }}>
                  <PlayCircleOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                  <div>正在加载视频...</div>
                </div>
              ) : currentVideo ? (
                <ReactPlayer
                  url={currentVideo.videoUrl}
                  width="100%"
                  height="100%"
                  playing={playing}
                  playbackRate={playbackRate}
                  controls={true}
                  light={currentVideo.coverImageUrl}
                  onPlay={() => setPlaying(true)}
                  onPause={() => setPlaying(false)}
                  onError={(error) => {
                    console.error('视频播放错误:', error);
                    message.error('视频播放失败');
                  }}
                  config={{
                    file: {
                      attributes: {
                        style: { width: '100%', height: '100%' },
                        controlsList: 'nodownload',
                        disablePictureInPicture: false
                      }
                    }
                  }}
                />
              ) : (
                <div style={{
                  color: '#ffffff',
                  fontSize: '16px',
                  textAlign: 'center'
                }}>
                  <PlayCircleOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                  <div>暂无视频</div>
                </div>
              )}

              {/* 播放速度控制 */}
              {currentVideo && (
                <div style={{
                  position: 'absolute',
                  bottom: '10px',
                  right: '10px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  background: 'rgba(0, 0, 0, 0.7)',
                  padding: '6px 12px',
                  borderRadius: '20px',
                  color: '#ffffff',
                  fontSize: '12px'
                }}>
                  <span>速度:</span>
                  <select
                    value={playbackRate}
                    onChange={(e) => setPlaybackRate(parseFloat(e.target.value))}
                    style={{
                      background: 'transparent',
                      border: 'none',
                      color: '#ffffff',
                      fontSize: '12px',
                      cursor: 'pointer',
                      outline: 'none'
                    }}
                  >
                    <option value={0.5} style={{ background: '#333', color: '#fff' }}>0.5x</option>
                    <option value={0.75} style={{ background: '#333', color: '#fff' }}>0.75x</option>
                    <option value={1} style={{ background: '#333', color: '#fff' }}>1x</option>
                    <option value={1.25} style={{ background: '#333', color: '#fff' }}>1.25x</option>
                    <option value={1.5} style={{ background: '#333', color: '#fff' }}>1.5x</option>
                    <option value={2} style={{ background: '#333', color: '#fff' }}>2x</option>
                  </select>
                </div>
              )}
            </div>
          </div>

          {/* 右侧视频列表区域 - 20%宽度 */}
          <div style={{
            width: '20%',
            background: '#2f2f2f',
            padding: '20px',
            display: 'flex',
            flexDirection: 'column',
            gap: '16px'
          }}>
            <div>
              <h4 style={{
                margin: '0 0 16px 0',
                color: '#ffffff',
                fontSize: '16px',
                fontWeight: '600'
              }}>
                相关知识点视频
              </h4>
            </div>

            {/* 视频列表 */}
            <div className="video-list-container" style={{ flex: 1, overflowY: 'auto' }}>
              {videoList.map((video, index) => (
                <div
                  key={video.id}
                  onClick={() => handleVideoSelect(index)}
                  className={`video-list-item ${currentVideoIndex === index ? 'active' : ''}`}
                  style={{
                    padding: '12px',
                    margin: '0 0 8px 0',
                    background: currentVideoIndex === index ? '#404040' : 'transparent',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px'
                  }}
                >
                  {/* 橙色序号标签 */}
                  <div style={{
                    background: '#ff6b35',
                    color: '#ffffff',
                    fontSize: '12px',
                    fontWeight: '600',
                    padding: '4px 8px',
                    borderRadius: '4px',
                    minWidth: '24px',
                    textAlign: 'center'
                  }}>
                    {index + 1}
                  </div>

                  {/* 视频信息 */}
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <div style={{
                      fontWeight: '500',
                      color: '#ffffff',
                      fontSize: '13px',
                      marginBottom: '6px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}>
                      {video.title.replace(/_\d{14}$/, '')}
                    </div>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '12px',
                      fontSize: '11px',
                      color: '#cccccc'
                    }}>
                      {video.durationSeconds && (
                        <span>
                          {Math.floor(video.durationSeconds / 60).toString().padStart(2, '0')}:{(video.durationSeconds % 60).toString().padStart(2, '0')}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>






          </div>
        </div>
      </Modal>
    </div>
  );
};

ResultScreen.propTypes = {
  knowledgePoint: PropTypes.shape({
    id: PropTypes.number,
    name: PropTypes.string,
    chapterName: PropTypes.string,
    subjectName: PropTypes.string
  }),
  progress: PropTypes.number,
  userAnswers: PropTypes.object,
  questions: PropTypes.array,
  onRestart: PropTypes.func,
  onViewQuestions: PropTypes.func,
  sessionId: PropTypes.number, // 可选的练习会话ID
  testStartTime: PropTypes.number, // 测试开始时间戳
  testEndTime: PropTypes.number // 测试结束时间戳
};

export default ResultScreen; 